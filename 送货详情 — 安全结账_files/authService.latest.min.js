(()=>{var e={1989:(e,t,o)=>{var n=o(1789),r=o(401),i=o(7667),a=o(1327),c=o(1866);function s(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=r,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},8407:(e,t,o)=>{var n=o(7040),r=o(4125),i=o(2117),a=o(7518),c=o(4705);function s(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=r,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},7071:(e,t,o)=>{var n=o(852)(o(5639),"Map");e.exports=n},3369:(e,t,o)=>{var n=o(4785),r=o(1285),i=o(6e3),a=o(9916),c=o(5265);function s(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=r,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},2705:(e,t,o)=>{var n=o(5639).Symbol;e.exports=n},9932:e=>{e.exports=function(e,t){for(var o=-1,n=null==e?0:e.length,r=Array(n);++o<n;)r[o]=t(e[o],o,e);return r}},8470:(e,t,o)=>{var n=o(7813);e.exports=function(e,t){for(var o=e.length;o--;)if(n(e[o][0],t))return o;return-1}},4239:(e,t,o)=>{var n=o(2705),r=o(9607),i=o(2333),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?r(e):i(e)}},8565:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e,o){return null!=e&&t.call(e,o)}},9454:(e,t,o)=>{var n=o(4239),r=o(7005);e.exports=function(e){return r(e)&&"[object Arguments]"==n(e)}},8458:(e,t,o)=>{var n=o(3560),r=o(5346),i=o(3218),a=o(346),c=/^\[object .+?Constructor\]$/,s=Function.prototype,u=Object.prototype,l=s.toString,d=u.hasOwnProperty,f=RegExp("^"+l.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||r(e))&&(n(e)?f:c).test(a(e))}},531:(e,t,o)=>{var n=o(2705),r=o(9932),i=o(1469),a=o(3448),c=n?n.prototype:void 0,s=c?c.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return r(t,e)+"";if(a(t))return s?s.call(t):"";var o=t+"";return"0"==o&&1/t==-Infinity?"-0":o}},1811:(e,t,o)=>{var n=o(1469),r=o(5403),i=o(5514),a=o(9833);e.exports=function(e,t){return n(e)?e:r(e,t)?[e]:i(a(e))}},4429:(e,t,o)=>{var n=o(5639)["__core-js_shared__"];e.exports=n},1957:(e,t,o)=>{var n="object"==typeof o.g&&o.g&&o.g.Object===Object&&o.g;e.exports=n},5050:(e,t,o)=>{var n=o(7019);e.exports=function(e,t){var o=e.__data__;return n(t)?o["string"==typeof t?"string":"hash"]:o.map}},852:(e,t,o)=>{var n=o(8458),r=o(7801);e.exports=function(e,t){var o=r(e,t);return n(o)?o:void 0}},9607:(e,t,o)=>{var n=o(2705),r=Object.prototype,i=r.hasOwnProperty,a=r.toString,c=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,c),o=e[c];try{e[c]=void 0;var n=!0}catch(e){}var r=a.call(e);return n&&(t?e[c]=o:delete e[c]),r}},7801:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},222:(e,t,o)=>{var n=o(1811),r=o(5694),i=o(1469),a=o(5776),c=o(1780),s=o(327);e.exports=function(e,t,o){for(var u=-1,l=(t=n(t,e)).length,d=!1;++u<l;){var f=s(t[u]);if(!(d=null!=e&&o(e,f)))break;e=e[f]}return d||++u!=l?d:!!(l=null==e?0:e.length)&&c(l)&&a(f,l)&&(i(e)||r(e))}},1789:(e,t,o)=>{var n=o(4536);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},401:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},7667:(e,t,o)=>{var n=o(4536),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var o=t[e];return"__lodash_hash_undefined__"===o?void 0:o}return r.call(t,e)?t[e]:void 0}},1327:(e,t,o)=>{var n=o(4536),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:r.call(t,e)}},1866:(e,t,o)=>{var n=o(4536);e.exports=function(e,t){var o=this.__data__;return this.size+=this.has(e)?0:1,o[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},5776:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,o){var n=typeof e;return!!(o=null==o?9007199254740991:o)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<o}},5403:(e,t,o)=>{var n=o(1469),r=o(3448),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var o=typeof e;return!("number"!=o&&"symbol"!=o&&"boolean"!=o&&null!=e&&!r(e))||(a.test(e)||!i.test(e)||null!=t&&e in Object(t))}},7019:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},5346:(e,t,o)=>{var n,r=o(4429),i=(n=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";e.exports=function(e){return!!i&&i in e}},7040:e=>{e.exports=function(){this.__data__=[],this.size=0}},4125:(e,t,o)=>{var n=o(8470),r=Array.prototype.splice;e.exports=function(e){var t=this.__data__,o=n(t,e);return!(o<0)&&(o==t.length-1?t.pop():r.call(t,o,1),--this.size,!0)}},2117:(e,t,o)=>{var n=o(8470);e.exports=function(e){var t=this.__data__,o=n(t,e);return o<0?void 0:t[o][1]}},7518:(e,t,o)=>{var n=o(8470);e.exports=function(e){return n(this.__data__,e)>-1}},4705:(e,t,o)=>{var n=o(8470);e.exports=function(e,t){var o=this.__data__,r=n(o,e);return r<0?(++this.size,o.push([e,t])):o[r][1]=t,this}},4785:(e,t,o)=>{var n=o(1989),r=o(8407),i=o(7071);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||r),string:new n}}},1285:(e,t,o)=>{var n=o(5050);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},6e3:(e,t,o)=>{var n=o(5050);e.exports=function(e){return n(this,e).get(e)}},9916:(e,t,o)=>{var n=o(5050);e.exports=function(e){return n(this,e).has(e)}},5265:(e,t,o)=>{var n=o(5050);e.exports=function(e,t){var o=n(this,e),r=o.size;return o.set(e,t),this.size+=o.size==r?0:1,this}},4523:(e,t,o)=>{var n=o(8306);e.exports=function(e){var t=n(e,(function(e){return 500===o.size&&o.clear(),e})),o=t.cache;return t}},4536:(e,t,o)=>{var n=o(852)(Object,"create");e.exports=n},2333:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},5639:(e,t,o)=>{var n=o(1957),r="object"==typeof self&&self&&self.Object===Object&&self,i=n||r||Function("return this")();e.exports=i},5514:(e,t,o)=>{var n=o(4523),r=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(r,(function(e,o,n,r){t.push(n?r.replace(i,"$1"):o||e)})),t}));e.exports=a},327:(e,t,o)=>{var n=o(3448);e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-Infinity?"-0":t}},346:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},7813:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},8721:(e,t,o)=>{var n=o(8565),r=o(222);e.exports=function(e,t){return null!=e&&r(e,t,n)}},5694:(e,t,o)=>{var n=o(9454),r=o(7005),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,s=n(function(){return arguments}())?n:function(e){return r(e)&&a.call(e,"callee")&&!c.call(e,"callee")};e.exports=s},1469:e=>{var t=Array.isArray;e.exports=t},3560:(e,t,o)=>{var n=o(4239),r=o(3218);e.exports=function(e){if(!r(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},1780:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},3218:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},7005:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},3448:(e,t,o)=>{var n=o(4239),r=o(7005);e.exports=function(e){return"symbol"==typeof e||r(e)&&"[object Symbol]"==n(e)}},8306:(e,t,o)=>{var n=o(3369);function r(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var o=function(){var n=arguments,r=t?t.apply(this,n):n[0],i=o.cache;if(i.has(r))return i.get(r);var a=e.apply(this,n);return o.cache=i.set(r,a)||i,a};return o.cache=new(r.Cache||n),o}r.Cache=n,e.exports=r},9833:(e,t,o)=>{var n=o(531);e.exports=function(e){return null==e?"":n(e)}},1035:function(e,t,o){var n,r,i;i=function(){"use strict";var e,t,o=["webkit","Moz","ms","O"],n={};function r(e,t){var o,n=document.createElement(e||"div");for(o in t)n[o]=t[o];return n}function i(e){for(var t=1,o=arguments.length;t<o;t++)e.appendChild(arguments[t]);return e}function a(o,r,i,a){var c=["opacity",r,~~(100*o),i,a].join("-"),s=.01+i/a*100,u=Math.max(1-(1-o)/r*(100-s),o),l=e.substring(0,e.indexOf("Animation")).toLowerCase(),d=l&&"-"+l+"-"||"";return n[c]||(t.insertRule("@"+d+"keyframes "+c+"{0%{opacity:"+u+"}"+s+"%{opacity:"+o+"}"+(s+.01)+"%{opacity:1}"+(s+r)%100+"%{opacity:"+o+"}100%{opacity:"+u+"}}",t.cssRules.length),n[c]=1),c}function c(e,t){var n,r,i=e.style;if(void 0!==i[t=t.charAt(0).toUpperCase()+t.slice(1)])return t;for(r=0;r<o.length;r++)if(void 0!==i[n=o[r]+t])return n}function s(e,t){for(var o in t)e.style[c(e,o)||o]=t[o];return e}function u(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)void 0===e[n]&&(e[n]=o[n])}return e}function l(e,t){return"string"==typeof e?e:e[t%e.length]}var d,f={lines:12,length:7,width:5,radius:10,scale:1,corners:1,color:"#000",opacity:1/4,rotate:0,direction:1,speed:1,trail:100,fps:20,zIndex:2e9,className:"spinner",top:"50%",left:"50%",shadow:!1,hwaccel:!1,position:"absolute"};function p(e){this.opts=u(e||{},p.defaults,f)}if(p.defaults={},u(p.prototype,{spin:function(t){this.stop();var o=this,n=o.opts,i=o.el=r(null,{className:n.className});if(s(i,{position:n.position,width:0,zIndex:n.zIndex,left:n.left,top:n.top}),t&&t.insertBefore(i,t.firstChild||null),i.setAttribute("role","progressbar"),o.lines(i,o.opts),!e){var a,c=0,u=(n.lines-1)*(1-n.direction)/2,l=n.fps,d=l/n.speed,f=(1-n.opacity)/(d*n.trail/100),p=d/n.lines;!function e(){c++;for(var t=0;t<n.lines;t++)a=Math.max(1-(c+(n.lines-t)*p)%d*f,n.opacity),o.opacity(i,t*n.direction+u,a,n);o.timeout=o.el&&setTimeout(e,~~(1e3/l))}()}return o},stop:function(){var e=this.el;return e&&(clearTimeout(this.timeout),e.parentNode&&e.parentNode.removeChild(e),this.el=void 0),this},lines:function(t,o){var n,c=0,u=(o.lines-1)*(1-o.direction)/2;function d(e,t){return s(r(),{position:"absolute",width:o.scale*(o.length+o.width)+"px",height:o.scale*o.width+"px",background:e,boxShadow:t,transformOrigin:"left",transform:"rotate("+~~(360/o.lines*c+o.rotate)+"deg) translate("+o.scale*o.radius+"px,0)",borderRadius:(o.corners*o.scale*o.width>>1)+"px"})}for(;c<o.lines;c++)n=s(r(),{position:"absolute",top:1+~(o.scale*o.width/2)+"px",transform:o.hwaccel?"translate3d(0,0,0)":"",opacity:o.opacity,animation:e&&a(o.opacity,o.trail,u+c*o.direction,o.lines)+" "+1/o.speed+"s linear infinite"}),o.shadow&&i(n,s(d("#000","0 0 4px #000"),{top:"2px"})),i(t,i(n,d(l(o.color,c),"0 0 1px rgba(0,0,0,.1)")));return t},opacity:function(e,t,o){t<e.childNodes.length&&(e.childNodes[t].style.opacity=o)}}),"undefined"!=typeof document){d=r("style",{type:"text/css"}),i(document.getElementsByTagName("head")[0],d),t=d.sheet||d.styleSheet;var h=s(r("group"),{behavior:"url(#default#VML)"});!c(h,"transform")&&h.adj?function(){function e(e,t){return r("<"+e+' xmlns="urn:schemas-microsoft.com:vml" class="spin-vml">',t)}t.addRule(".spin-vml","behavior:url(#default#VML)"),p.prototype.lines=function(t,o){var n=o.scale*(o.length+o.width),r=2*o.scale*n;function a(){return s(e("group",{coordsize:r+" "+r,coordorigin:-n+" "+-n}),{width:r,height:r})}var c,u=-(o.width+o.length)*o.scale*2+"px",d=s(a(),{position:"absolute",top:u,left:u});function f(t,r,c){i(d,i(s(a(),{rotation:360/o.lines*t+"deg",left:~~r}),i(s(e("roundrect",{arcsize:o.corners}),{width:n,height:o.scale*o.width,left:o.scale*o.radius,top:-o.scale*o.width>>1,filter:c}),e("fill",{color:l(o.color,t),opacity:o.opacity}),e("stroke",{opacity:0}))))}if(o.shadow)for(c=1;c<=o.lines;c++)f(c,-2,"progid:DXImageTransform.Microsoft.Blur(pixelradius=2,makeshadow=1,shadowopacity=.3)");for(c=1;c<=o.lines;c++)f(c);return i(t,d)},p.prototype.opacity=function(e,t,o,n){var r=e.firstChild;n=n.shadow&&n.lines||0,r&&t+n<r.childNodes.length&&(r=(r=(r=r.childNodes[t+n])&&r.firstChild)&&r.firstChild)&&(r.opacity=o)}}():e=c(h,"animation")}return p},e.exports?e.exports=i():void 0===(r="function"==typeof(n=i)?n.call(t,o,t,e):n)||(e.exports=r)}},t={};function o(n){var r=t[n];if(void 0!==r)return r.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,o),i.exports}o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";o.r(n),o.d(n,{auth:()=>R,default:()=>N});var e=function(e){if(!e)throw new TypeError("Invalid url",e);var t=document.implementation.createHTMLDocument(""),o=t.createElement("a");if(o.href=e,t.body.appendChild(o),":"===o.protocol||!/:/.test(o.href))throw new TypeError("Invalid URL");return o.protocol+"//"+o.host},t=function(e){var t=function(e){var t=[];return Array.isArray(e)?e.forEach((function(e,o,n){t.push(e)})):t.push(e),t}(e);return{permittedList:t,notPermittedList:[]}},r=function(e,t,o){var n;if(null!=e&&void 0!==e.destinationDomain){if(void 0===t)return void 0!==o&&void 0!==o.error&&-4===o.error.code;n=t.origin;for(var r=0;r<e.destinationDomain.length;++r)if(n===e.destinationDomain[r])return!0}return!1},i=function(e,t,o){var n=e.permittedList,r=e.notPermittedList,i=!1,a=!1;if(o&&{getRegisteredProcedures:!0,receivePingRequest:!0}[o])return!0;for(var c=0;c<n.length;++c)if(t===n[c]){i=!0;break}for(var s=0;s<r.length;++s)if(t===n[c]){a=!0;break}return i&&!a};function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}const c=function(){if("undefined"==typeof JSON||void 0===JSON.stringify||void 0===JSON.parse)throw"pmrpc requires the JSON library";var e=[];if(void 0===window.postMessage&&void 0===window.onconnect)throw"pmrpc requires the HTML5 cross-document messaging and worker APIs";function t(){return"object"===a(window.crypto)&&"function"==typeof window.crypto.getRandomValues&&"function"==typeof Uint32Array?window.crypto.getRandomValues(new Uint32Array(1))[0]/Math.pow(2,32):Math.random()}function o(){for(var e=[],o=0;o<36;o++)e[o]="0123456789ABCDEF"[Math.floor(16*t())];return e[14]="4",e[19]="89AB"[Math.floor(4*t())],e[8]=e[13]=e[18]=e[23]="-",e.join("")}var n="pmrpc.";function c(e){e.trim()&&(n=e+".")}var s=function(e){if(window.sessionStorage){var t=e.publicProcedureName+"."+n,o={procedure:e.publicProcedureName,status:e.status,description:e.message,namespace:e.namespace?e.namespace:n};sessionStorage.setItem(t,JSON.stringify(o))}};function u(e,t,o,n){if(!(o instanceof Array)){var r=e.toString(),i=r.substring(r.indexOf("(")+1,r.indexOf(")"));i=""===i?[]:i.split(", ");for(var a={},c=0;c<i.length;c++)a[i[c]]=c;var s=[];for(var u in o){if(void 0===a[u])throw"No such param: "+u;s[a[u]]=o[u]}o=s}return void 0!==n&&(o=o.concat(n)),e.apply(t,o)}function l(e){return n+JSON.stringify(e)}function d(){var e={jsonrpc:"2.0"};return e}function f(e,t,o){var n=d();return n.method=e,n.params=t,void 0!==o&&(n.id=o),n}function p(e,t,o){var n={};return n.code=e,n.message=t,n.data=o,n}function h(e,t,o){var n=d();return n.id=o,null==e?n.result="undefined"===t?null:t:n.error=e,n}t();var m={},g={},v={};function y(e){return!(e.publicProcedureName in v)&&(m[e.publicProcedureName]={publicProcedureName:e.publicProcedureName,procedure:e.procedure,context:e.procedure.context,isAsync:void 0!==e.isAsynchronous&&e.isAsynchronous,acl:void 0!==e.acl?e.acl:{permittedList:["(.*)"],notPermittedList:[]}},!0)}function w(e){return!(e in v)&&(delete m[e],!0)}function b(e){return m[e]}function A(t,o){var i=t.id,a=g[i],c={receivePingRequest:"rPR",getRegisteredProcedures:"gRP",ready:"r",config:"i"},u=(new Date).getTime(),l={a:u,b:"",c:"rRE",f:"nPN",e:"f2:uRFRpcC"};if(null!=a){if(delete g[i],!r(a,o,t))return l.e="f2:cAF",void e.push(l);if(void 0===t.error){var d={destination:a.destination,publicProcedureName:a.publicProcedureName,params:a.params,status:"success",namespace:n,returnValue:t.result};e.push({a:u,b:a.callId,c:"rRS",h:c[a.publicProcedureName]?c[a.publicProcedureName]:a.publicProcedureName,e:"f2:rRS"}),a.onSuccess(d)}else{var f={destination:a.destination,publicProcedureName:a.publicProcedureName,params:a.params,status:"error",namespace:n,message:t.error.message+" "+t.error.data};s(f),e.push({a:u,b:a.callId,c:"rRE",f:f.message,h:c[a.publicProcedureName]?c[a.publicProcedureName]:a.publicProcedureName,e:"f2:rRE"}),a.onError(f)}}else e.push(l)}function k(e){if(e.retries&&e.retries<0)throw new Error("number of retries must be 0 or higher");var t=[];if(void 0===e.destination||null===e.destination||"workerParent"===e.destination)t=[{context:null,type:"workerParent"}];else if("publish"===e.destination)t=S();else if(e.destination instanceof Array)for(var n=0;n<e.destination.length;n++)"workerParent"===e.destination[n]?t.push({context:null,type:"workerParent"}):void 0!==e.destination[n].frames?t.push({context:e.destination[n],type:"window"}):t.push({context:e.destination[n],type:"worker"});else void 0!==e.destination.frames?t.push({context:e.destination,type:"window"}):t.push({context:e.destination,type:"worker"});for(n=0;n<t.length;n++){var r={destination:t[n].context,destinationDomain:void 0===e.destinationDomain?["*"]:"string"==typeof e.destinationDomain?[e.destinationDomain]:e.destinationDomain,publicProcedureName:e.publicProcedureName,onSuccess:void 0!==e.onSuccess?e.onSuccess:function(){},onError:void 0!==e.onError?e.onError:function(){},retries:void 0!==e.retries?e.retries:5,timeout:void 0!==e.timeout?e.timeout:500,status:"requestNotSent"},i=void 0===e.onError&&void 0===e.onSuccess,a=void 0!==e.params?e.params:[],c=o();g[c]=r,r.callId=c,r.message=i?f(e.publicProcedureName,a):f(e.publicProcedureName,a,c),T(c)}}function I(t,o,n,r){var i={receivePingRequest:"rPR",getRegisteredProcedures:"gRP",ready:"r",config:"i"};if(void 0!==r?e.push({a:(new Date).getTime(),b:r.callId,c:r.logType||"req",h:i[r.publicProcedureName]?i[r.publicProcedureName]:r.publicProcedureName,e:r.calledAt}):e.push({a:(new Date).getTime(),b:"",c:"uPM",h:"",e:"f3:uRpcC"}),null==t)self.postMessage(l(o));else{if(void 0!==t.frames)return t.postMessage(l(o),n);t.postMessage(l(o))}}function T(e,t){self.setTimeout((function(){var o=g[e];if(void 0!==o&&(!t||"pinging"===o.status))if(o.retries<=-1)A(h(p(-4,"Application error.","Destination unavailable. "+o.timeout),null,e));else{if("requestSent"===o.status)return;if(0===o.retries||"available"===o.status){o.status="requestSent",o.retries=-1,g[e]=o;for(var n=0;n<o.destinationDomain.length;n++)o.calledAt="wSR:SR",I(o.destination,o.message,o.destinationDomain[n],o),self.setTimeout((function(){T(e)}),o.timeout)}else{o.status="pinging",o.retries=o.retries-1;var r={destination:o.destination,publicProcedureName:"receivePingRequest",onSuccess:function(t){!0===t.returnValue&&void 0!==g[e]&&"pinging"===g[e].status&&(g[e].status="available",T(e))},params:[o.publicProcedureName],retries:0,destinationDomain:o.destinationDomain};o.destinationDomain&&(r.destinationDomain=o.destinationDomain),k(r),g[e]=o,window.setTimeout((function(){g[e]&&"pinging"===g[e].status&&T(e,!0)}),o.timeout/o.retries)}}}),10)}if(!("window"in window))throw"Pmrpc must be loaded within a browser window";var x,_,P,O=(x=function(t){var o=t.event;if(o&&o.data&&"string"==typeof o.data&&0===o.data.indexOf(n)){var r,a=(r=o.data,JSON.parse(r.substring(n.length))),c={data:o.data,source:o.source,origin:o.origin,shouldCheckACL:!0};if(void 0!==a.method){e.push({a:(new Date).getTime(),b:a.id,c:"rR",h:a.method,e:"f1:mR"});var s=function(e,t,o){if("2.0"!==e.jsonrpc)return h(p(-32600,"Invalid request.","The recived JSON is not a valid JSON-RPC 2.0 request."),null,null);var n=e.id,r=b(e.method);if(void 0===r)return void 0===n?null:h(p(-32601,"Method not found.","The requested remote procedure does not exist or is not available."),null,n);if(t.shouldCheckACL&&!i(r.acl,t.origin,r.publicProcedureName))return void 0===n?null:h(p(-2,"Application error.","Access denied on server."),null,n);try{if(r.isAsync){var a=function(e){I(t.source,h(null,e,n),t.origin)},c=function(e){I(t.source,h(p(-1,"Application error.",e.message),null,n),t.origin)};return u(r.procedure,r.context,e.params,[a,c,t]),null}try{var s=u(r.procedure,r.context,e.params,[t]);return void 0===n?null:h(null,s,n)}catch(e){return h(p(-5,"Procedure error.","Procedure thrown exception"),null,n)}}catch(e){return void 0===n?null:e.match("^(No such param)")?h(p(-32602,"Invalid params.",e.message),null,n):h(p(-1,"Application error.",e.message),null,n)}}(a,c);null!==s&&I(c.source,s,c.origin,{callId:a.id,logType:"res",publicProcedureName:a.method,calledAt:"f1:f4"})}else A(a,c)}},_=null,P="window",function(e){x({event:e,source:_,destinationType:P})});function S(){var e=[];return function(){var e=[];if("undefined"!=typeof window){e.push({context:window.top,type:"window"});for(var t=0;void 0!==e[t];t++)for(var o=e[t],n=0;n<o.context.frames.length;n++)e.push({context:o.context.frames[n],type:"window"})}else e.push({context:window,type:"workerParent"});return e}().concat(e)}return function(e,t,o,n){"addEventListener"in e?e.addEventListener(t,o,n):e.attachEvent("on"+t,o)}(window,"message",O,!1),y({publicProcedureName:"receivePingRequest",procedure:function(e){return void 0!==b(e)}}),y({publicProcedureName:"getRegisteredProcedures",procedure:function(){var e=[],t=void 0!==window.frames?window.location.protocol+"//"+window.location.host+(""!==window.location.port?":"+window.location.port:""):"";for(var o in m)o in v||e.push({publicProcedureName:m[o].publicProcedureName,acl:m[o].acl,origin:t});return e}}),v={getRegisteredProcedures:null,receivePingRequest:null},{register:y,unregister:w,call:function(e){e.namespace&&c(e.namespace),k(e)},discover:function(e){var t=null;if(void 0===e.destination){t=S();for(var o=0;o<t.length;o++)t[o]=t[o].context}else t=e.destination;var n=void 0===e.originRegex?"(.*)":e.originRegex,r=void 0===e.nameRegex?"(.*)":e.nameRegex,i=t.length,a=[];this.call({destination:t,destinationDomain:"*",publicProcedureName:"getRegisteredProcedures",onSuccess:function(t){i--,function(e,t){for(var o=0;o<e.length;o++)e[o].origin.match(new RegExp(n))&&e[o].publicProcedureName.match(new RegExp(r))&&a.push({publicProcedureName:e[o].publicProcedureName,destination:t,procedureACL:e[o].acl,destinationOrigin:e[o].origin})}(t.returnValue,t.destination),0===i&&e.callback(a)},onError:function(t){0===--i&&e.callback(a)}})},setNamespace:c,sessionLog:s,getMessageLog:function(){return void 0!==e?e:void 0},getMessageStringLog:function(){var t={receivePingRequest:"rPR",getRegisteredProcedures:"gRP",ready:"r",config:"i",getConfig:"gC"};if(void 0!==e){var o=[],n=[];return e.forEach((function(e){n=[],Object.keys(e).forEach((function(o){var r=e[o];"a"===o?r=(e[o]+"").substring(5):"b"===o?r=e[o].substring(0,8):"h"===o&&(r=t[r]?t[r]:r);n.push(o+":"+r)})),o.push(n.join(" "))})),o.join("|")}}}};var s=o(1035),u=o(8721),l={},d={},f={},p=function(e){return d[e]||(d[e]=[]),d[e]},h=function(e,t){l[e]&&(l[e]=null),function(e,t){p(e).forEach((function(e){return e(t)}))}(e,t||"closed"),function(e){d[e]&&(d[e]=[])}(e),f[e]&&(clearInterval(f[e]),f[e]=null)},m=function(e,t){var o=window.innerWidth?window.innerWidth:document.documentElement.clientWidth?document.documentElement.clientWidth:screen.width,n=window.innerHeight?window.innerHeight:document.documentElement.clientHeight?document.documentElement.clientHeight:screen.height;return{left:o/2-e/2+window.screenLeft,top:n/2-t/2+window.screenTop}},g=700,v=700,y=m(g,v).left,w=m(g,v).top,b={strWindowFeatures:"width=".concat(g,",height=").concat(v,",left=").concat(y,",top=").concat(w,",resizable=no,location=no,menubar=no"),windowName:"AppleAuthentication"},A=function(e){!function(e,t){l[e]&&("function"==typeof l[e].close&&l[e].close(),h(e,t))}(b.windowName,e)},k=function(e){return A("closed_trigger_new_signing_flow"),function(e,t,o){var n=window.open(e,t,o);return n&&(l[t]=n,f[t]=setInterval((function(){n.closed&&h(t,"closed_by_user")}),300)),n}(e,b.windowName,b.strWindowFeatures)},I=function(){return e=b.windowName,!!l[e];var e},T=function(e){return function(e,t){p(e).push(t)}(b.windowName,e)};function x(e){return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},x(e)}function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}var P,O={state:"authorize",authorizePath:"/auth/authorize/signin",codePath:"/auth/authorize/validate",enableFirstPartyOAuth:!1,code:void 0,oauthState:void 0,appleOAuth:{requestor:{}},completeData:void 0},S={name:"aid-auth-widget",nameSpace:"pmrpc",path:"/auth/signin",initiatePath:"/signin/initiate",isFailedToLoadFirstPartyOAuth:!1,getUrlPath:function(e){return e.features&&e.features.useSignInInitiate?S.initiatePath:O.enableFirstPartyOAuth?"authorize"===O.state?O.authorizePath:"code"===O.state?O.codePath:S.path:S.path},urlParamTransform:function(e,t){var o=[],n={widgetDomain:!0,widgetKey:!0};if(O.enableFirstPartyOAuth&&"fail"!==O.state){o.push(["frame_id",t.iframeId]);for(var r=0;r<e.length;r++)n[e[r][0]]||o.push([e[r][0],e[r][1]]),"iframeId"===e[r][0]&&void 0===O.oauthState&&(t.appleOAuth.requestor.state=e[r][1],O.oauthState=e[r][1]);if(!t.appleOAuth.requestor.id)throw new TypeError("requestor.id is either not provided or is not defined.");o.push(["client_id",t.appleOAuth.requestor.id]),t.appleOAuth.requestor.scope&&o.push(["scope",t.appleOAuth.requestor.scope]),t.appleOAuth.requestor.teamId&&o.push(["team_id",t.appleOAuth.requestor.teamId]),t.appleOAuth.requestor.redirectURI&&o.push(["redirect_uri",t.appleOAuth.requestor.redirectURI]),t.appleOAuth.requestor.responseType&&o.push(["response_type",t.appleOAuth.requestor.responseType]),t.appleOAuth.requestor.responseMode&&o.push(["response_mode",t.appleOAuth.requestor.responseMode]),t.features&&"number"==typeof t.features.accountInd&&o.push(["account_ind",t.features.accountInd]),O.oauthState&&o.push(["state",O.oauthState]),"code"===O.state&&(o.push(["code",O.code]),"string"==typeof t.qrc&&""!==t.qrc&&o.push(["qrc",t.qrc]),void 0!==t.alternateTokenCookie&&""!==t.alternateTokenCookie&&o.push(["altToken",t.alternateTokenCookie.toString()]),"string"==typeof t.OAuthClientId&&""!==t.OAuthClientId&&o.push(["rc_oauthid",t.OAuthClientId]),"string"==typeof t.OAuthToken&&""!==t.OAuthToken&&o.push(["rc_oauthtoken",t.OAuthToken]),"boolean"==typeof t.enableOIDCGrantCode&&o.push(["oauthRequireGrantCode",t.enableOIDCGrantCode?"true":"false"]))}else o=function(e){for(var t=[],o=0;o<e.length;o++)t.push([e[o][0],e[o][1]]);return t}(e),void 0!==t.requestorAppId&&""!==t.requestorAppId&&o.push(["appId",t.requestorAppId]);return void 0!==t.resetCRToken&&""!==t.resetCRToken&&o.push(["key",t.resetCRToken]),void 0!==t.resetCRLang&&""!==t.resetCRLang&&o.push(["language",t.resetCRLang]),void 0!==t.domainId&&""!==t.domainId&&o.push(["rv",t.domainId]),void 0===t.context||"TEST_IDP"!==t.context.toUpperCase()&&"MICROSOFT_OBO"!==t.context.toUpperCase()||o.push(["ct","1"]),o.push(["authVersion","latest"]),o}},C=new function(o){o.name=o.name||"widget-loader",o.nameSpace=o.nameSpace||"widgetLoader",o.frameLoadTimeout=o.frameLoadTimeout||6e4,o.configTimeout=o.configTimeout||3e4,o.path=o.path||"",o.urlTransform=o.urlTransform||function(e){return e},o.widgetWindowReference=void 0;var n,r,i,a=new c,l={},d=!1,f=!1,p=!1,h=!1,m=void 0,g=[],v=["ready","config"],y=[],w={},b={lines:12,length:12,width:5,radius:14,scale:.4,corners:1,color:"#000",opacity:.25,rotate:0,direction:1,speed:1.7,trail:60,fps:20,zIndex:2e9,className:"spinner",top:"50%",left:"50%",shadow:!1,hwaccel:!1,position:"absolute"},_=new s(b);a.context=self.name||"Parent",a||(a=new c),a.setNamespace(o.nameSpace);var P=function(e,t,o){if(e&&""!==e.trim()&&(e=e.trim().toUpperCase()),"START_WITH"===e){if("string"==typeof t)"LITE"===t.toString().toUpperCase().trim()&&(b.color="#fff");else if("object"===x(t))if(t.autoThemeAdjust&&"function"==typeof window.matchMedia){var n=window.matchMedia("(prefers-color-scheme: dark)");n.matches&&(b.color="#fff"),n.addListener((function(e){var t=e.matches;b.color=t?"#fff":"#000"}))}else"dark-standard"!==t.defaultTheme&&"lite"!==t.defaultTheme||(b.color="#fff");_.spin(document.getElementById(o))}else"STOP"===e&&_.stop()},O=function(){h=!1,n=setTimeout((function(){d||(d=!0,q(o.name),"function"==typeof l.config.loadFailed&&l.config.loadFailed({error:"timeout"}))}),o.frameLoadTimeout)},S=function(o,n){if(void 0!==l.config&&l.config.serviceURL){i=e(l.config.serviceURL);var r,s=!!w[o];r=t(s?[window.location.origin,i]:i),a||(a=new c),a.register({publicProcedureName:o,procedure:n,acl:r})}else g.push({__key__:o,__callback__:n})},C=function(e){return JSON.parse(e.replace(o.nameSpace+".","")).method},R=function(e){var t,n,r,i=e.serviceURL,a=o.path,c=[],s=[];o.getUrlPath&&(a=o.getUrlPath(e));var u,d,f;if(n=i+=a,i=i+"?widgetKey="+e.serviceKey,c.push(["widgetKey",e.serviceKey]),u=e.acts,d=[],f="",u&&u.length>0&&(u.forEach((function(e){e.widgetKey&&e.domain&&d.push([e.widgetKey,e.domain])})),d.length>0&&(f=encodeURIComponent(JSON.stringify({d})))),(r=f)&&(i+="&acts="+r,c.push(["acts",r])),e.locale&&(i+="&language="+e.locale,c.push(["language",e.locale])),e.features&&e.features.useEyebrowTextbox&&(i+="&useEyebrowTextbox="+e.features.useEyebrowTextbox,c.push(["useEyebrowTextbox",e.features.useEyebrowTextbox])),e.skVersion&&!isNaN(e.skVersion)&&(i+="&skVersion="+e.skVersion,c.push(["skVersion",e.skVersion])),void 0!==e.devAppDomain&&""!==e.devAppDomain&&(i+="&widgetDomain="+e.devAppDomain,c.push(["widgetDomain",e.devAppDomain])),e.onDemand2FAToken&&(i+="&odc=1",c.push(["odc","1"])),t=void 0!==e.iframeId?e.iframeId:function(){for(var e="auth-"+Math.random().toString(36).substr(2,8),t=1;t<=3;t++)e+="-"+Math.random().toString(36).substr(2,4);return e+"-"+Math.random().toString(36).substr(2,8)}(),l.iframeId=t,e.iframeId=t,i+="&iframeId="+t,c.push(["iframeId",t]),o.urlParamTransform){c=o.urlParamTransform(c,e);for(var p=0;p<c.length;p++)s.push(c[p].join("="));l.srcUrl=n+"?"+s.join("&")}else l.srcUrl=o.urlTransform(i,e);return l.srcUrl},N=function(e){var t={result:!1},o=[],n=function(e,t){var n=!0;return t.map((function(t){u(e,t)||(n=!1,o.push(t))})),n};return t.result=n(e,["serviceKey","serviceURL","callbacks.onAuthSuccess"]),t.result&&(e.usePopup||(t.result=n(e,["containerId"])),e.context&&"TEST_IDP"===e.context.trim().toUpperCase()&&(t.result=n(e,["context","domainName","gsWebToken","alternateDSID","orgId","entityId"])),e.context&&"MICROSOFT_OBO"===e.context.trim().toUpperCase()&&(t.result=n(e,["context","gsWebToken","orgId","alternateDSID","entityId"]))),t.result||(t.missingPaths=o),t},D=function(e){if(""===e.locale||e.locale,void 0===l.config.waitAnimation||""===l.config.waitAnimation.trim()||l.config.waitAnimation&&l.config.waitAnimation.toUpperCase().trim(),e.features,""===e.serviceKey||!e.serviceKey)throw new TypeError("serviceKey is either not provided or is not defined.");if(""===e.serviceURL||!e.serviceURL)throw new TypeError("serviceURL is either not provided or is not defined.");if(!(e.usePopup||""!==e.containerId&&e.containerId))throw new TypeError("containerId is either not provided or is not defined.");e.callbacks&&e.callbacks.completed&&"function"==typeof e.callbacks.completed&&(!e.callbacks||!e.callbacks.failed||e.callbacks.failed)},E=function(t,n){var r=document.createElement("iframe");r.src=R(t),r.width=t.width||"100%",r.height=t.height||"100%",r.id=n+"-iFrame",r.name=n,r.scrolling="no",r.frameBorder="0",r.setAttribute("role","none"),r.setAttribute("referrerpolicy","strict-origin-when-cross-origin"),r.allow="publickey-credentials-get "+e(t.serviceURL)+";bluetooth "+e(t.serviceURL),window.document.getElementById(t.containerId).appendChild(r),o.widgetWindowReference=window.frames[n]},L=function(e){T((function(t){if(("closed_by_user"===t||"closed_trigger_new_signing_flow"==t)&&e.callbacks&&e.callbacks.onAuthCancel){var o="closed"===t?"USER_CLOSE_POPUP":"USER_TRIGGER_NEW_SIGNIN_FLOW";e.callbacks.onAuthCancel({result:o})}}))},F=function(e,t){var n=R(e),r=k(n);r?o.widgetWindowReference=r:d||(d=!0,"function"==typeof l.config.loadFailed&&l.config.loadFailed({error:"popup_blocked_by_browser"}))},q=function(e){I()?A():function(e){var t=document.getElementById(e+"-iFrame");null!==t&&"IFRAME"===t.nodeName&&t.parentNode&&t.parentNode.removeChild(t)}(e)},j=function(){S("getConfig",(function(e){return y.push("getConfig"),!0,clearTimeout(n),"function"==typeof l.config.callbacks.getConfig&&l.config.callbacks.getConfig(e),P("STOP"),y.indexOf(v[0])>y.indexOf(v[1])&&(r=setTimeout((function(){f||p||(p=!0,q(o.name),"function"==typeof l.config.callbacks.failed&&l.config.callbacks.failed(e))}),l.config.configTimeout)),l.config})),(l.config.features&&!0===l.config.features.autoResize||"2"===l.config.signInVersion)&&S("resize",(function(e,t){var n;null!==(n=void 0===t?document.getElementById(o.name+"-iFrame"):document.querySelector("#"+t+" #"+o.name+"-iFrame"))&&100<e.height&&(n.height=Math.ceil(e.height).toString())})),S("widgetFailedToLoad",(function(e){y.push("widgetFailedToLoad"),d||(d=!0,clearTimeout(n),q(o.name),"function"==typeof l.config.callbacks.loadFailed&&l.config.callbacks.loadFailed(e))})),S("ready",(function(e){y.push("ready"),f=!0,clearTimeout(r),"function"==typeof l.config.callbacks.ready&&l.config.callbacks.ready(e)})),S("configFailed",(function(e){y.push("configFailed"),p||(p=!0,clearTimeout(r),q(o.name),"function"==typeof l.config.callbacks.failed&&l.config.callbacks.failed(e))})),S("failed",(function(e){y.push("failed"),"function"==typeof l.config.callbacks.failed&&l.config.callbacks.failed(e)})),S("completed",(function(e){y.push("completed"),"function"==typeof l.config.callbacks.completed&&l.config.callbacks.completed(e)})),S("exit",(function(e){y.push("exit"),"function"==typeof l.config.callbacks.exit&&l.config.callbacks.exit(e)})),S("error",(function(e){y.push("error"),"function"==typeof l.config.callbacks.error&&l.config.callbacks.error(e)})),S("log",(function(e){y.push("log"),"function"==typeof l.config.callbacks.log&&l.config.callbacks.log(e)}))},M=function(){for(var e in g)g.hasOwnProperty(e)&&S(g[e].__key__,g[e].__callback__);U()},U=function(){window.addEventListener("message",(function(e){if(void 0!==e&&void 0!==e.data&&"string"==typeof e.data&&-1!==e.data.indexOf(o.nameSpace)){var t=C(e.data),n=e.origin||e.originalEvent.origin;for(var r in g)g.hasOwnProperty(r)&&t&&t.toLowerCase().trim()===g[r].__key__.toLowerCase().trim()&&n===i&&y.push(g[r].__key__)}}),!1)};return l.load=function(e){!1,d=!1,f=!1,p=!1,h=!1,m=void 0,y=[],o.widgetWindowReference=void 0,void 0===e.returnUrl&&void 0!==e.returnURL&&(e.returnUrl=e.returnURL);var t=N(e);if(t.result){l.config=e,D(l.config),void 0!==l.config.usePopup&&l.config.usePopup||(void 0===l.config.waitAnimation||""===l.config.waitAnimation.trim()||l.config.waitAnimation&&"FALSE"!==l.config.waitAnimation.toUpperCase().trim())&&P("START_WITH",l.config.theme,l.config.containerId),I()&&A(),void 0!==l.config.usePopup&&l.config.usePopup?(F(l.config,o.name),L(l.config,o.name)):E(l.config,o.name),j(),M();var n=Number(e.frameLoadTimeout);o.frameLoadTimeout=!isNaN(n)&&o.frameLoadTimeout<n?n:o.frameLoadTimeout,h||O(),m=l.config.serviceURL}else if(e.callbacks&&e.callbacks.onAuthFailure){var r="";t.missingPaths.map((function(e){r+="\t "+e+"\t "})),e.callbacks.onAuthFailure({result:{code:"MISSING_MANDATORY_INIT_OPTIONS",message:"Missing mandatory initOptions: ["+r+"] \n Refer the documentation for more information. \n",data:{}}})}},l.teardown=function(){a.call({destination:o.widgetWindowReference,publicProcedureName:"teardownWidget",onSuccess:function(e){q(o.name),clearTimeout(n),clearTimeout(r),l.config.callbacks.onAuthTeardown()},onError:function(e){q(o.name),clearTimeout(n),clearTimeout(r),l.config.callbacks.onAuthTeardown()},destinationDomain:m,retries:3})},l.resume2FA=function(){a.call({destination:o.widgetWindowReference,publicProcedureName:"resume2FA",onSuccess:function(e){l.config.callbacks.onAuthResume2FA&&l.config.callbacks.onAuthResume2FA(!0)},onError:function(e){l.config.callbacks.onAuthResume2FA&&l.config.callbacks.onAuthResume2FA(!1)},destinationDomain:m,retries:3})},l.addListener=S,l.getMessageStringLog=a.getMessageStringLog,l.buildIFrame=E,l.clearTimeout=function(){clearTimeout(n),h=!0},l.setLoadTimeout=O,l.reLoadIFrame=function(){q(o.name),E(l.config,o.name),O()},l.addAliasToListener=function(e,t,n){if(void 0===t||""===t.trim())throw new Error("[listener] is required.");if(void 0===e||""===e.trim())throw new Error("[alias] is required.");if(t.toLowerCase().trim()===e.toLowerCase().trim())throw new Error("[alias] [listener] names cannot be same.");if("function"!=typeof n)throw new Error("[callback] is required and should be a function.");if(w[t])throw new Error("");w[t]=e,window.addEventListener("message",(function(n){if(void 0!==n&&void 0!==n.data&&"string"==typeof n.data&&-1!==n.data.indexOf(o.nameSpace)){var r=C(n.data),c=n.origin||n.originalEvent.origin;r&&r.toLowerCase().trim()===e.toLowerCase().trim()&&c===i&&a.call({destination:window,destinationDomain:window.location.origin,publicProcedureName:t,namespace:o.nameSpace,onSuccess:function(e){},onError:function(e){}})}}),!1),S(e,n)},l.destroyIFrame=q,l.closePopup=A,l}(S);C.addListener("complete",(function(e){C.config.usePopup&&C.closePopup(),"function"==typeof C.config.callbacks.onAuthSuccess&&C.config.callbacks.onAuthSuccess(e,{iframeId:C.iframeId})}));C.addListener("ready",(function(e){"function"==typeof C.config.callbacks.onAuthReady&&C.config.callbacks.onAuthReady(e,{iframeId:C.iframeId});var t=document.getElementById(S.name+"-iFrame");null!==t&&void 0!==e.iframeTitle&&""!==e.iframeTitle&&(t.title=e.iframeTitle)})),C.addListener("authFailedToLoad",(function(e){var t=C.getMessageStringLog(),o=C.iframeId;C.config.usePopup&&C.closePopup(),"function"==typeof C.config.callbacks.onAuthFailure&&C.config.callbacks.onAuthFailure(e,{logMessage:t,iframeId:o})})),C.addListener("passwordAuthDone",(function(e,t){"function"==typeof C.config.callbacks.onPasswordAuthDone&&C.config.callbacks.onPasswordAuthDone(e,t,{iframeId:C.iframeId})})),C.addListener("authError",(function(e,t){"function"==typeof C.config.callbacks.onAuthError&&C.config.callbacks.onAuthError(e,t)})),C.addListener("repairDidStart",(function(e){"function"==typeof C.config.callbacks.onRepairStart&&C.config.callbacks.onRepairStart(e,{iframeId:C.iframeId})})),C.addListener("authDidCancel",(function(e){C.config.usePopup&&C.closePopup(),"function"==typeof C.config.callbacks.onAuthCancel&&C.config.callbacks.onAuthCancel(e,{iframeId:C.iframeId})})),C.addListener("authTeardown",(function(e){C.config.usePopup&&C.closePopup(),"function"==typeof C.config.callbacks.onAuthTeardown&&C.config.callbacks.onAuthTeardown(e,{iframeId:C.iframeId})})),C.addListener("authWidgetDidChangeFlow",(function(e){"function"==typeof C.config.callbacks.onAuthWidgetDidChangeFlow&&C.config.callbacks.onAuthWidgetDidChangeFlow(e,{iframeId:C.iframeId})})),C.addListener("authWidgetWillChangeFlow",(function(e){"function"==typeof C.config.callbacks.onAuthWidgetWillChangeFlow&&C.config.callbacks.onAuthWidgetWillChangeFlow(e,{iframeId:C.iframeId})})),C.addListener("appleIDSuccess",(function(e,t,o){"function"==typeof C.config.callbacks.onAppleIDSuccess&&C.config.callbacks.onAppleIDSuccess(e,t,o)})),C.addAliasToListener("config","getConfig",(function(e){return{data:C.config}})),window.addEventListener("message",(function(e){var t,o,n=e.origin||(e.originalEvent?e.originalEvent.origin:void 0),r="MISSING_DATA",i="LOAD_TIMEOUT",a="FAIL_TO_VERIFY_CREDENTIALS";if(C.config&&C.config.features&&C.config.serviceURL&&!C.config.onDemand2FAToken&&C.config.features.enableFirstPartyOAuth&&n===function(e){if(!e)throw new TypeError("Invalid url",e);var t=document.implementation.createHTMLDocument(""),o=t.createElement("a");if(o.href=e,t.body.appendChild(o),":"===o.protocol||!/:/.test(o.href))throw new TypeError("Invalid URL");return o.protocol+"//"+o.host}(C.config.serviceURL))if("SOAuthorizationDidStart"===e.data)C.clearTimeout(),"function"==typeof C.config.callbacks.onNativeTakeOverDidStart&&C.config.callbacks.onNativeTakeOverDidStart({iframeId:C.iframeId});else if("SOAuthorizationDidCancel"===e.data)"function"==typeof C.config.callbacks.onNativeTakeOverDidCancel&&C.config.callbacks.onNativeTakeOverDidCancel({iframeId:C.iframeId}),C.setLoadTimeout();else if(e.data&&"AppSSOTakeoverDidComplete"===e.data.event){if(C.clearTimeout(),"function"==typeof C.config.callbacks.onNativeTakeOverDidComplete&&C.config.callbacks.onNativeTakeOverDidComplete({iframeId:C.iframeId}),void 0===e.data.authorization||void 0===e.data.authorization.grant_code)return O.state="fail","function"==typeof C.config.callbacks.onNativeTakeOverError&&C.config.callbacks.onNativeTakeOverError({code:r},{iframeId:C.iframeId}),C.config.authServiceErrorCode=r,void C.reLoadIFrame();try{t=atob(e.data.authorization.grant_code)}catch(o){t=e.data.authorization.grant_code}O.code=t,e.data.authorization&&(o=e.data.authorization.state),e.data.data&&(O.completeData=e.data.data);var c={serviceURL:C.config.serviceURL,serviceKey:C.config.serviceKey,code:t,appleOAuth:{requestor:O.appleOAuth.requestor},width:"0",height:"0",iframeId:C.iframeId,containerId:C.config.containerId,state:C.config.state};void 0!==C.config.alternateTokenCookie&&""!==C.config.alternateTokenCookie&&(c.alternateTokenCookie=C.config.alternateTokenCookie),void 0!==C.config.requestorContext&&"string"==typeof C.config.requestorContext.OAuthClientId&&(c.OAuthClientId=C.config.requestorContext.OAuthClientId),void 0!==C.config.requestorContext&&"string"==typeof C.config.requestorContext.OAuthToken&&(c.OAuthToken=C.config.requestorContext.OAuthToken),C.config.features&&(c.enableOIDCGrantCode=!!C.config.features.enableOIDCGrantCode),void 0!==C.config.qrcToken&&""!==C.config.qrcToken&&(c.qrc=C.config.qrcToken),C.config.acts&&(c.acts=C.config.acts),O.state="code",C.buildIFrame(c,"widget-bridge"),P=setTimeout((function(){O.state="fail",clearTimeout(P),"function"==typeof C.config.callbacks.onNativeTakeOverError&&C.config.callbacks.onNativeTakeOverError({code:i},{iframeId:C.iframeId}),C.config.authServiceErrorCode=i,C.destroyIFrame("widget-bridge"),C.reLoadIFrame()}),6e4)}else if(e.data&&e.data.event&&"WidgetBridgeComplete"===e.data.event){var s={};if(o=e.data.state,clearTimeout(P),o&&(s.state=o),C.config.features&&C.config.features.enableOIDCGrantCode){s={grantCode:e.data.token,state:o};"object"===_(O.completeData)&&(s.bridge={key:O.completeData.sharing_key,mid:O.completeData.mid}),C.config.callbacks.onAuthSuccess(s,{iframeId:C.iframeId})}else"object"===_(O.completeData)&&(s.bridge={key:O.completeData.sharing_key,mid:O.completeData.mid}),C.config.callbacks.onAuthSuccess(s,{iframeId:C.iframeId})}else e.data&&e.data.event&&"WidgetBridgeFailed"===e.data.event&&(O.state="fail",clearTimeout(P),"function"==typeof C.config.callbacks.onNativeTakeOverError&&C.config.callbacks.onNativeTakeOverError({code:e.data.code?e.data.code:a},{iframeId:C.iframeId}),C.config.authServiceErrorCode={code:e.data.code?e.data.code:a,errorMessage:e.data.errorMessage||"",errorCode:e.data.errorCode||""},C.destroyIFrame("widget-bridge"),C.reLoadIFrame())}));window.AppleID=window.AppleID||{},window.AppleID.service=window.AppleID.service||{},window.AppleID.service.auth=window.AppleID.service.auth||{},window.AppleID.service.auth.init||(window.AppleID.service.auth.init=function(e){O.state="authorize",O.enableFirstPartyOAuth=!1,O.code=void 0,O.oauthState=void 0,O.appleOAuth={requestor:{}},!e.onDemand2FAToken&&e.features&&e.features.enableFirstPartyOAuth&&(O.enableFirstPartyOAuth=!0,function(e){e.appleOAuth={requestor:{id:e.serviceKey,redirectURI:window.location.origin,responseMode:"web_message",responseType:"code"}},void 0!==e.state&&(e.appleOAuth.requestor.state=e.state,O.oauthState=e.appleOAuth.requestor.state),void 0!==e.devAppDomain&&""!==e.devAppDomain&&(e.appleOAuth.requestor.redirectURI=e.devAppDomain),O.appleOAuth.requestor=e.appleOAuth.requestor}(e)),C.load(e)}),window.AppleID.service.auth.teardown=window.AppleID.service.auth.teardown||C.teardown,window.AppleID.service.auth.resume2FA=window.AppleID.service.auth.resume2FA||C.resume2FA,window.AppleID.service.auth.closePopup=window.AppleID.service.auth.closePopup||C.closePopup;var R={init:C.load,teardown:C.teardown,closePopup:C.closePopup,resume2FA:C.resume2FA};const N=C.load})(),window.idmsapis=n})();