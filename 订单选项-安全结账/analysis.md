# `checkout_reproducer_Mac.py` 代码分析报告

这是一个相当复杂的自动化脚本，主要目标是模拟用户在苹果中国官网（Apple Store）的购买流程，从自动登录、添加商品到购物车，再到完成结账步骤。它综合运用了浏览器自动化（Selenium/Undetected Chromedriver）和后端API请求（Requests）两种技术，以提高稳定性和效率。

下面是详细的逐行代码功能分析：

### **第一部分: 导入模块 (Imports)**

```python
# 导入用于发送HTTP网络请求的库
import requests
# 导入用于处理JSON格式数据的库
import json
# 导入用于正则表达式操作的库，方便从文本中提取信息
import re
# 导入日志记录库，用于在程序运行时输出信息
import logging
# 导入用于生成随机数的库
import random
# 导入包含字符串常量的库
import string
# 导入时间库，用于添加延迟等操作
import time
# 导入一个经过特殊处理的Selenium WebDriver，用于防止被网站检测为自动化程序
import undetected_chromedriver as uc
# 从Selenium库中导入By，用于指定查找网页元素的方式（如按ID、CSS选择器等）
from selenium.webdriver.common.by import By
# 从Selenium库中导入WebDriverWait，用于等待网页上的特定条件发生（如元素出现）
from selenium.webdriver.support.ui import WebDriverWait
# 从Selenium库中导入expected_conditions，定义了多种常见的等待条件
from selenium.webdriver.support import expected_conditions as EC
# 从Selenium库中导入特定的异常类型，用于处理找不到元素或超时的错误
from selenium.common.exceptions import TimeoutException, NoSuchElementException
# 导入与操作系统交互的库，用于文件和目录操作
import os
# 再次导入json库并重命名，以清晰区分其在凭据处理中的用途
import json as _json_for_creds
# 导入日期时间库，用于生成时间戳
import datetime
# 导入哈希算法库，用于加密计算
import hashlib
# 导入HMAC（基于哈希的消息认证码）算法库
import hmac
# 导入用于生成安全随机数的库，常用于加密
import secrets
# 导入Base64编码库
import base64
# 从URL处理库中导入几个函数，用于解析和操作URL
from urllib.parse import urlparse, parse_qs, unquote
# 尝试导入Tkinter库，这是一个标准的Python GUI库
try:
	import tkinter as _tk
	# 从Tkinter中导入simpledialog用于创建简单的输入对话框，messagebox用于显示消息框
	from tkinter import simpledialog as _simpledialog, messagebox as _messagebox
	# 设置一个标志，表示Tkinter库可用
	_TK_AVAILABLE = True
# 如果导入失败（例如在没有图形界面的服务器上）
except Exception:
	# 设置标志，表示Tkinter库不可用
	_TK_AVAILABLE = False
```

### **第二部分: 全局配置 (Configuration)**

```python
# --- 配置区 ---
# 配置日志记录的基本设置：日志级别为INFO，格式包含时间、级别和消息
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# 创建一个日志记录器实例
logger = logging.getLogger(__name__)

# 设置要购买的商品数量
PURCHASE_QUANTITY = 2

# 设置是否以“无头模式”运行浏览器（即没有图形界面）。结账登录时会自动变为有界面模式。
HEADLESS = True

# 设置是否优先使用API进行登录；如果为True，则不使用浏览器登录作为备用方案
PREFER_API_LOGIN = True
DISABLE_BROWSER_LOGIN_FALLBACK = True
# 设置在第一次遇到双重认证(2FA)后是否停止
STOP_AFTER_FIRST_2FA = False
# 设置处理302重定向时的最大重试次数
MAX_302_RETRIES = 1

# 创建一个空字典，用于动态记录从IDMSA（苹果身份验证服务）返回的关键HTTP头部信息
_IDMSA_HEADER_HINTS: dict = {}

# 设置是否允许使用GUI弹窗来输入2FA（双重认证）验证码
USE_UI_2FA_PROMPT = True
# 设置当IDMSA服务返回5xx服务器错误时，是否将请求和响应的关键信息写入文件以供调试
DEBUG_IDMSA_DUMP = True

# SRP（安全远程密码协议）相关常量，注释表明其基于RFC 5054标准
# 登陆自动操作:账号 密码,并验证是否存在双重认证,若存在自动处理
```

### **第三部分: 核心功能函数 (Functions)**

这部分是脚本的核心，包含了所有自动化操作的函数。

#### **登录与认证相关函数**

```python
# 定义一个函数，等待页面完全加载
def wait_for_page_load(driver, timeout=30):
    try:
        # 使用WebDriverWait等待，直到执行JavaScript脚本'return document.readyState'的结果为'complete'
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script('return document.readyState') == 'complete'
        )
    # 如果在指定时间内页面未加载完成
    except Exception:
        # 记录一条警告日志
        logger.warning("等待页面加载完成超时。")

# 定义处理双重认证(2FA)的函数
def handle_two_factor_auth(driver, account, log_message):
    try:
        # 记录日志，表明开始处理2FA
        log_message("ℹ️ 开始处理双重认证...")
        # 调用函数弹出GUI窗口让用户输入2FA验证码
        code = _prompt_2fa_code_ui()
        # 如果用户没有输入验证码，或者输入的长度不是6位
        if not code or len(code) != 6:
            # 记录错误日志
            log_message("❌ 未提供有效6位验证码。")
            # 返回失败
            return False
        
        # 记录日志，表明正在输入验证码
        log_message("⌨️ 输入2FA验证码...")
        # 查找所有验证码输入框（使用CSS选择器）
        code_inputs = driver.find_elements(By.CSS_SELECTOR, "input.form-security-code-input")
        # 如果没找到
        if not code_inputs:
            # 使用备用的CSS选择器再次尝试查找
            code_inputs = driver.find_elements(By.CSS_SELECTOR, "div.verify-code-input input")

        # 遍历找到的每个输入框
        for i, input_field in enumerate(code_inputs):
            # 在每个输入框中输入验证码的一位数字
            input_field.send_keys(code[i])
            # 短暂休眠0.1秒，模拟人工输入
            time.sleep(0.1)
        # 记录日志，表明输入完成
        log_message("✅ 2FA验证码输入完毕。")
        # 返回成功
        return True
    # 如果过程中出现任何异常
    except Exception as e:
        # 记录详细的错误日志
        log_message(f"❌ 处理双重认证时出错: {e}")
        # 返回失败
        return False

# 定义处理“信任浏览器”按钮的函数
def handle_trust_browser(driver, log_message):
    try:
        # 记录日志
        log_message("🔄 正在查找“信任浏览器”按钮...")
        # 使用WebDriverWait等待“信任”按钮出现并可点击（最长等待15秒）
        trust_button = WebDriverWait(driver, 15).until(
            EC.element_to_be_clickable((By.XPATH, "//button[normalize-space()='信任']"))
        )
        # 记录日志
        log_message("✅ 找到“信任”按钮，正在点击...")
        # 使用JavaScript来点击按钮，这种方式更稳定
        driver.execute_script("arguments[0].click();", trust_button)
        # 记录日志
        log_message("✅ 已点击信任浏览器。")
    # 如果找不到按钮或出现其他异常
    except Exception:
        # 记录一条提示信息，说明可能不需要点击
        log_message("ℹ️ 未找到“信任浏览器”按钮，或无需点击。")

# 核心的自动登录函数
def auto_login(driver, account, log_message, login_url):
    # 初始化iframes变量
    iframes = None
    try:
        # 记录日志，导航到登录页
        log_message(f"导航到登录页: {login_url}")
        driver.get(login_url)
        # 记录当前页面标题
        log_message(f"页面标题: {driver.title}")

        # 如果不是在安全的结账登录页，则处理隐私弹窗
        if not ("secure" in login_url and "signIn" in login_url):
            handle_privacy_popup(driver, log_message)

            # 保存调试用的截图和HTML源码
            ts = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            pre_iframe_screenshot = f"pre_iframe_wait_{ts}.png"
            pre_iframe_html = f"pre_iframe_wait_{ts}.html"
            driver.save_screenshot(pre_iframe_screenshot)
            with open(pre_iframe_html, "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            log_message(f"已保存iframe加载前的调试文件: {pre_iframe_screenshot}, {pre_iframe_html}")

        # 等待登录表单所在的iframe出现，并切换进去
        log_message("⏳ 等待并切换到登录iframe...")
        try:
            # 等待iframe可用并切换（最长20秒）
            WebDriverWait(driver, 20).until(
                EC.frame_to_be_available_and_switch_to_it((By.TAG_NAME, "iframe"))
            )
            log_message("✅ 已切换到iframe")
        # 如果超时
        except TimeoutException:
            # 记录日志，说明将尝试在主页面操作
            log_message("ℹ️ 未在20秒内检测到iframe，将尝试在主页面操作")

        # 等待邮箱输入框出现
        email_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.ID, "account_name_text_field"))
        )
        # 清空输入框
        email_input.clear()
        # 输入邮箱地址
        email_input.send_keys(account["email"])
        log_message("✅ 已输入邮箱")
        # 等待“继续”按钮可点击
        continue_button = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable((By.ID, "sign-in"))
        )
        # 使用JS点击按钮
        driver.execute_script("arguments[0].click();", continue_button)
        log_message("✅ 已点击继续按钮")
        try:
            # 等待密码输入框可点击
            password_input = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.ID, "password_text_field"))
            )
            # 使用JS将密码框滚动到视野内
            driver.execute_script("arguments[0].scrollIntoView(true);", password_input)
            # 清空密码框
            password_input.clear()
            # 输入密码
            password_input.send_keys(account["password"])
            log_message("✅ 已输入密码")
            # 等待登录按钮可点击
            sign_in_button = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.ID, "sign-in"))
            )
            # 将登录按钮滚动到视野内
            driver.execute_script("arguments[0].scrollIntoView(true);", sign_in_button)
            # 点击登录按钮
            driver.execute_script("arguments[0].click();", sign_in_button)
            log_message("✅ 已点击登录按钮")

            # 保存当前URL，用于后续判断页面是否跳转
            old_url = driver.current_url
            # 初始化2FA处理状态为False
            two_fa_handled = False

            log_message("🔍 智能检测双重认证界面或页面跳转...")

            try:
                # 定义一个复合等待条件函数
                def check_2fa_or_url_change(driver):
                    # 条件1: 检查URL是否变化
                    if driver.current_url != old_url:
                        return "url_changed"

                    # 条件2: 检查是否存在2FA输入框
                    try:
                        # 先在主页面检查
                        code_inputs = driver.find_elements(By.CSS_SELECTOR, "input.form-security-code-input")
                        if len(code_inputs) >= 6:
                            # 同时检查页面源码是否包含2FA关键词
                            page_text = driver.page_source
                            keywords = ["双重认证", "验证码", "安全验证"]
                            if any(k in page_text for k in keywords):
                                return "2fa_detected_main"

                        # 再到iframe里检查
                        iframes = driver.find_elements(By.TAG_NAME, "iframe")
                        if iframes:
                            # ... (此处为在iframe中查找2FA界面的复杂逻辑)
                            # ...
                            pass # 省略具体实现细节

                    except Exception:
                        pass

                    # 如果以上条件都不满足，返回False，让WebDriverWait继续等待
                    return False

                # 使用WebDriverWait等待，直到上述函数返回非False的值（最长20秒）
                result = WebDriverWait(driver, 20).until(check_2fa_or_url_change)

                # 根据返回结果进行处理
                if result == "url_changed":
                    log_message("✅ 智能检测：登录后URL已变化，无需双重认证")
                elif result in ["2fa_detected_main", "2fa_detected_iframe"]:
                    # ... (处理在主页面或iframe中检测到2FA的情况)
                    # ...
                    pass # 省略具体实现细节

            # 如果智能检测超时或出错
            except Exception as e:
                log_message(f"⚠️ 智能检测超时或出错，使用备用检测方案: {str(e)}")
                # 直接尝试调用2FA处理函数作为备用方案
                two_fa_result = handle_two_factor_auth(driver, account, log_message)
                if two_fa_result:
                    handle_trust_browser(driver, log_message)
                    two_fa_handled = True
                else:
                    log_message("⚠️ 备用检测也未发现双重认证，可能登录成功或存在其他问题")

            # 如果处理了2FA，则等待页面跳转
            if two_fa_handled:
                # ... (循环检查URL是否变化，确认2FA后页面跳转成功)
                # ...
                pass # 省略具体实现细节

        # 如果输入密码或点击登录时出错
        except Exception as e:
            log_message(f"❌ 密码输入或登录按钮点击失败：{str(e)}")
            # 保存错误截图
            driver.save_screenshot("error_password_input.png")
            # 尝试切回主内容区
            try:
                driver.switch_to.default_content()
            except: pass
            # 返回失败
            return False
        
        # 登录流程结束，切回主内容区
        driver.switch_to.default_content()
        log_message("✅ 已切回主frame")
        
        # 登录成功后，保存cookies到文件
        try:
            all_cookies = driver.get_cookies()
            if save_cookies_to_file(all_cookies):
                log_message("✅ 已保存登录cookies，下次可跳过2FA验证")
        except Exception as e:
            log_message(f"⚠️ 保存cookies失败: {e}")
        
        # 返回成功
        return True
    # 如果整个登录流程出现异常
    except Exception as e:
        log_message(f"❌ 普通登录页面自动填充失败: {repr(e)}")
        driver.save_screenshot("error_auto_login.png")
        # 尝试切回主内容区
        try:
            driver.switch_to.default_content()
        except:
            pass
        # 返回失败
        return False

# 定义处理隐私弹窗的函数
def handle_privacy_popup(driver, log_message):
    """处理隐私弹窗"""
    try:
        log_message("🔄 检查并处理隐私弹窗...")
        # 等待页面加载
        wait_for_page_load(driver, 10)
        # 智能等待弹窗出现，而不是固定等待
        try:
            # 等待5秒，直到找到弹窗的特定元素或页面包含特定文本
            WebDriverWait(driver, 5).until(
                lambda d: d.find_element(By.CSS_SELECTOR, "div.rc-overlay.rs-signin-consent-overlay") or 
                         "Apple 和你的数据隐私" in d.page_source
            )
        except:
            # 如果没等到，就只休眠0.5秒
            time.sleep(0.5)
        
        # ... (此处为一系列复杂的、多重保障的弹窗检测逻辑)
        # ... (它会尝试通过CSS选择器、标题文本、iframe、页面源码等多种方式确认弹窗是否存在)
        privacy_detected = False 
        # ...
        
        # 如果最终确认检测到了弹窗
        if privacy_detected:
            log_message("ℹ️ 检测到隐私弹窗，开始处理...")
            try:
                # ... (此处为一系列WebDriverWait，等待弹窗内的各个元素出现并可点击)
                # ... (包括两个复选框和“同意并继续”按钮)
                # ... (然后用JS点击这些元素)
                log_message("✅ 成功处理隐私弹窗")
            except Exception as e:
                # 如果处理失败
                log_message(f"ℹ️ 未找到隐私弹窗内容或处理失败：{str(e)}")
                # 尝试用一种粗暴的方式隐藏弹窗的遮罩层
                try:
                    overlay = driver.find_element(By.CSS_SELECTOR, "div[data-core-overlay-cover]")
                    driver.execute_script("arguments[0].style.display = 'none';", overlay)
                except:
                    pass
        else:
            # 如果没检测到弹窗
            log_message("ℹ️ 无需处理隐私弹窗，继续执行后续步骤")
    # 如果整个函数出错
    except Exception as e:
        log_message(f"ℹ️ 处理隐私弹窗时发生错误：{str(e)}")
    # 无论成功失败，最后都确保切回主内容区
    finally:
        try:
            driver.switch_to.default_content()
        except:
            pass
```

#### **加密与凭据管理函数**

```python
# SRP（安全远程密码协议）的g值（一个公认的质数）
_SRP_g = 2

# 定义一个函数，计算数据的SHA-256哈希值
def _sha256(data: bytes) -> bytes:
	return hashlib.sha256(data).digest()

# 定义一个函数，将一个整数转换为指定长度的字节串（不足则在左侧补零）
def _pad_bytes(num: int, pad_len: int = 256) -> bytes:
	# ... (具体实现)
	pass

# 定义一个函数，对两个字节串进行异或(XOR)操作
def _bytes_xor(a: bytes, b: bytes) -> bytes:
	return bytes(x ^ y for x, y in zip(a, b))

# 定义一个函数，将一个整数补位后进行Base64编码
def _b64_of_int_padded(num: int) -> str:
	return base64.b64encode(_pad_bytes(num, 256)).decode('ascii')

# 定义全局变量，用于存储Apple ID和密码
APPLE_ID = None
APPLE_PWD = None

# 定义一个函数，从文件加载凭据
def _load_credentials_from_file():
    try:
        # 获取当前脚本所在的目录
        base_dir = os.path.dirname(os.path.abspath(__file__))
        # 构建凭据文件的完整路径
        cred_path = os.path.join(base_dir, 'credentials.json')
        
        # 如果文件存在
        if os.path.exists(cred_path):
            # 打开文件
            with open(cred_path, 'r', encoding='utf-8') as f:
                # 加载JSON数据
                data = json.load(f)
                # 获取apple_id和apple_pwd，兼容多种键名
                apple_id = data.get('apple_id') or data.get('APPLE_ID')
                apple_pwd = data.get('apple_pwd') or data.get('APPLE_PWD')
                # 返回凭据
                return apple_id, apple_pwd
    # 如果出错
    except Exception as e:
        print(f"读取凭据文件时出错：{e}")
        return None, None
    # 如果文件不存在
    return None, None

# 程序启动时直接调用函数加载凭据
APPLE_ID, APPLE_PWD = _load_credentials_from_file()

# 检查凭据是否加载成功
if APPLE_ID and APPLE_PWD:
    print("✅ 已从 credentials.json 成功加载 Apple ID 凭据")
else:
    print("❌ 未找到 credentials.json 或文件中缺少必要的凭据（apple_id 和 apple_pwd）")
```

#### **浏览器/会话管理函数**

```python
# 定义一个变量，用于手动设置atbtoken（添加到购物车的令牌）
MANUAL_ATBTOKEN = None

# 定义一个全局变量，用于存储浏览器驱动实例，避免重复创建
_global_driver = None

# 定义保存cookies的文件名
COOKIES_FILE = "apple_cookies.json"

# 定义Chrome用户配置文件的目录名
CHROME_PROFILE_DIR = "chrome_profile_apple"

# 定义保存cookies到文件的函数
def save_cookies_to_file(cookies, filename=COOKIES_FILE):
    """保存cookies到文件"""
    try:
        # 以写入模式打开文件
        with open(filename, 'w', encoding='utf-8') as f:
            # 将cookies数据以JSON格式写入文件，美化格式
            json.dump(cookies, f, ensure_ascii=False, indent=2)
        logger.info(f"✅ 已保存 {len(cookies)} 个cookies到 {filename}")
        return True
    except Exception as e:
        logger.error(f"保存cookies失败: {e}")
        return False

# 定义设置Chrome用户配置文件目录的函数
def setup_chrome_profile_directory():
    """设置Chrome用户配置文件目录"""
    try:
        # 获取当前脚本所在目录
        base_dir = os.path.dirname(os.path.abspath(__file__))
        # 构建配置文件的完整路径
        profile_path = os.path.join(base_dir, CHROME_PROFILE_DIR)

        # 如果目录不存在
        if not os.path.exists(profile_path):
            # 创建目录
            os.makedirs(profile_path)
            logger.info(f"✅ 已创建Chrome配置文件目录: {profile_path}")
        else:
            logger.info(f"✅ Chrome配置文件目录已存在: {profile_path}")

        # 返回路径
        return profile_path
    except Exception as e:
        logger.error(f"设置Chrome配置文件目录失败: {e}")
        return None

# 定义检查配置文件中是否已有登录状态的函数
def check_profile_login_state(profile_path):
    """检查Chrome配置文件中是否已有Apple ID登录状态"""
    try:
        # 定义关键文件的路径
        login_data_file = os.path.join(profile_path, "Default", "Login Data")
        preferences_file = os.path.join(profile_path, "Default", "Preferences")

        # 如果这些文件都存在
        if os.path.exists(login_data_file) and os.path.exists(preferences_file):
            # 检查文件大小，如果大于1KB，则认为有有效数据
            if os.path.getsize(login_data_file) > 1024 and os.path.getsize(preferences_file) > 1024:
                logger.info(f"✅ 检测到已有登录数据...")
                return True

        logger.info("❌ 未检测到有效的登录数据")
        return False
    except Exception as e:
        logger.error(f"检查配置文件登录状态失败: {e}")
        return False

# 定义执行首次登录的函数
def perform_first_time_login(driver):
    """执行首次登录，包括2FA验证，并保存到持久化配置文件"""
    try:
        logger.info("开始首次登录流程...")
        # 定义苹果官网登录页URL
        login_url = "https://appleid.apple.com.cn/sign-in"
        logger.info(f"导航到登录页面: {login_url}")
        driver.get(login_url)
        time.sleep(3)

        # 准备账号信息
        account = {"email": APPLE_ID, "password": APPLE_PWD}
        # 定义一个日志函数，方便区分日志来源
        log_func = lambda msg: logger.info(f"[首次登录] {msg}")

        # 调用通用的自动登录函数
        login_success = auto_login(driver, account, log_func, login_url)

        # 如果登录成功
        if login_success:
            logger.info("✅ 首次登录成功，登录状态已保存到持久化配置文件")
            # 访问购物袋页面验证登录状态
            driver.get("https://www.apple.com.cn/shop/bag")
            time.sleep(2)
            # 如果页面源码包含登出按钮或“购物袋”字样，说明验证成功
            if 'id="ac-gn-signout"' in driver.page_source or "购物袋" in driver.page_source:
                logger.info("✅ 登录状态验证成功")
                return True
            else:
                logger.warning("⚠️ 登录状态验证失败")
                return False
        else:
            logger.error("❌ 首次登录失败")
            return False
    except Exception as e:
        logger.error(f"首次登录过程中出错: {e}")
        return False

# 定义使用持久化配置文件进行登录的函数
def auto_login_with_persistent_profile(driver, account, log_message, login_url):
    """在结账时使用持久化配置文件进行登录"""
    try:
        log_message("🔄 检查持久化配置文件中的登录状态...")
        # 检查当前URL和页面源码，判断是否已经处于登录状态或已在结账流程中
        # ... (一系列检查逻辑)
        if any([...]): # 省略具体条件
            log_message("✅ 检测到已在结账页面，说明登录状态有效，无需重新登录")
            return True
        
        # 如果未登录，则调用通用登录函数
        log_message("❌ 未检测到登录状态，开始登录流程...")
        login_success = auto_login(driver, account, log_message, login_url)
        # ... (返回登录结果)
        
    except Exception as e:
        log_message(f"❌ 持久化配置文件登录过程中出错: {e}")
        return False

# 定义从当前页面获取最新STK（安全令牌）的函数
def get_current_stk_from_page(driver):
    """从当前页面获取最新的STK token"""
    try:
        # 尝试从ID为'init_data'的script标签中获取JSON数据并解析
        script_json = driver.execute_script("var e=document.getElementById('init_data'); return e? e.textContent : null;")
        if script_json:
            # ... (解析JSON找到STK)
            pass
        # 如果方法1失败，尝试从全局JavaScript变量window.init_data获取
        init_data = driver.execute_script("return window.init_data;")
        if isinstance(init_data, dict):
            # ... (解析字典找到STK)
            pass
        return None
    except Exception as e:
        logger.warning(f"获取页面STK失败: {e}")
        return None
```

#### **结账流程API自动化函数**

这部分函数通过直接调用苹果的后端API来完成结账步骤，而不是模拟浏览器点击，这样更快、更稳定。

```python
# 定义从结账页面提取地址信息的函数
def extract_address_from_page(driver):
    """从结账页面提取地址信息"""
    # ... (尝试从页面JS数据或文本中解析省、市、区信息)
    # ... (如果失败则返回一个默认地址)
    pass

# 定义处理“配送地址”步骤的函数
def handle_fulfillment_step(driver, session, stk, user_agent):
    """处理地址选择步骤 (fulfillment)，使用API请求方式"""
    try:
        logger.info("处理地址选择步骤（API方式）...")
        # 获取最新的STK令牌
        current_stk = get_current_stk_from_page(driver) or stk
        # 构建API的URL
        fulfillment_url = f"{base_url}/checkoutx/fulfillment"
        # 构建模拟真实浏览器的HTTP请求头
        headers = { ... }
        # 从页面提取或使用默认地址信息
        address_info = extract_address_from_page(driver)
        # ...
        # 构建要POST到服务器的表单数据
        fulfillment_data = { ... }
        # 构建URL的查询参数
        params = { ... }
        # 使用requests库发送POST请求
        response = session.post(fulfillment_url, headers=headers, data=fulfillment_data, params=params, timeout=30)
        # 检查响应状态码，判断是否成功
        if response.status_code == 200:
            # ... (检查页面是否已跳转到下一步)
            return True
        else:
            # ... (记录错误)
            return False
    except Exception as e:
        logger.error(f"地址选择步骤出错: {e}")
        return False

# 定义处理“配送方式”步骤的函数
def handle_shipping_step(driver, session, stk, user_agent):
    """处理配送方式步骤 (shipping)"""
    # ... (逻辑与handle_fulfillment_step类似，但URL、参数和数据不同)
    pass

# 定义处理“支付方式”步骤的函数
def handle_billing_step(driver, session, stk, user_agent):
    """处理支付方式步骤 (billing)"""
    # ... (逻辑与handle_fulfillment_step类似，但URL、参数和数据不同)
    pass

# 定义一个总函数，按顺序执行结账的各个API步骤
def proceed_with_checkout_steps(driver, session, stk, user_agent):
    """执行结账的后续步骤：地址选择 → 配送方式 → 支付方式 → 订单确认"""
    try:
        logger.info("🚀 开始执行结账后续步骤...")
        # 步骤1: 处理地址选择
        fulfillment_success = handle_fulfillment_step(driver, session, stk, user_agent)
        if not fulfillment_success: return False
        # 步骤2: 处理配送方式
        shipping_success = handle_shipping_step(driver, session, stk, user_agent)
        if not shipping_success: return False
        # 步骤3: 处理支付方式
        billing_success = handle_billing_step(driver, session, stk, user_agent)
        if not billing_success: return False
        
        logger.info("🎉 所有结账步骤完成！")
        # ... (验证最终状态)
        return True
    except Exception as e:
        logger.error(f"结账步骤执行失败: {e}")
        return False
```

#### **最终交互与验证函数**

```python
# 定义验证结账是否完成的函数
def verify_checkout_completion(driver):
    """验证结账是否真的完成，检查页面状态、URL、关键元素等"""
    # ... (通过检查URL、标题、页面内容、错误信息等多个指标来综合判断)
    pass

# 定义等待用户手动完成最后步骤的函数
def wait_for_manual_completion(driver, timeout_minutes=5):
    """等待用户手动完成最后的结账步骤，并监控页面变化"""
    try:
        logger.info(f"⏳ 等待用户手动完成结账（最多等待{timeout_minutes}分钟）...")
        # 循环监控当前URL和页面内容，看是否出现“感谢”、“成功”、“订单号”等字样
        while time.time() - start_time < timeout_seconds:
            # ... (检查逻辑)
            pass
        logger.info("⏰ 等待超时，停止监控")
        return False
    except Exception as e:
        logger.error(f"等待手动完成时出错: {e}")
        return False

# 定义交互式完成结账的函数
def interactive_checkout_completion(driver):
    """让用户选择是否需要手动完成最后步骤"""
    try:
        logger.info("🤔 结账API步骤已完成，但可能需要手动确认最后步骤")
        # ... (查找页面上可能的“提交订单”按钮)
        if submit_buttons:
            # 如果找到按钮，提示用户，并自动进入监控模式
            logger.info("🔄 程序将监控页面变化，请在浏览器中手动完成购买...")
            wait_for_manual_completion(driver, timeout_minutes=5)
        else:
            # 如果没找到按钮，提示用户手动检查，并等待2分钟后结束
            logger.info("💡 浏览器将保持打开状态，请手动完成购买")
            time.sleep(120)
        return False
    except Exception as e:
        logger.error(f"交互式结账完成时出错: {e}")
        return False

# 定义为结账流程设置专用浏览器的函数
def setup_browser_for_checkout():
    """为结账流程设置浏览器，使用持久化配置文件"""
    global _global_driver
    try:
        # ... (关闭旧的浏览器实例)
        # 创建Chrome选项
        options = uc.ChromeOptions()
        # 指定使用持久化配置文件目录
        profile_path = setup_chrome_profile_directory()
        if profile_path:
            options.add_argument(f'--user-data-dir={profile_path}')
        # 结账时强制使用有界面的浏览器
        # ... (添加其他优化参数)
        # 启动浏览器
        _global_driver = uc.Chrome(options=options, use_subprocess=True)
        # ... (设置超时)
        return _global_driver
    except Exception as e:
        logger.error(f"设置结账浏览器失败: {e}")
        return None
```

#### **添加购物车核心函数**

这部分是脚本的起点，负责获取关键令牌并把商品加入购物车。

```python
# 定义从文件加载cookies的函数
def load_cookies_from_file(filename=COOKIES_FILE):
    """从文件加载cookies"""
    # ... (打开文件并加载JSON)
    pass

# 定义检查cookies是否有效的函数
def is_cookies_valid(cookies):
    """检查cookies是否仍然有效（包含关键认证cookies）"""
    # ... (检查是否存在'aasp', 'dqsid'等关键cookie，并检查是否过期)
    pass

# --- 核心函数 (来自 add_to_bag.py) ---

# 定义以“潜行”模式获取atbtoken（添加到购物袋令牌）的函数
def get_atbtoken_stealth(product_url: str, use_saved_cookies=True, use_persistent_profile=True):
    """使用 undetected_chromedriver 访问页面，获取动态安全令牌 atbtoken"""
    global _global_driver
    logging.info("启动伪装浏览器以获取动态 atbtoken...")
    # ... (关闭旧浏览器)
    # 创建Chrome选项
    options = uc.ChromeOptions()
    # 如果使用持久化配置
    if use_persistent_profile:
        # 设置配置文件路径，并检查是否已有登录状态
        profile_path = setup_chrome_profile_directory()
        if profile_path:
            options.add_argument(f'--user-data-dir={profile_path}')
            has_existing_login = check_profile_login_state(profile_path)
            # ...
    
    # 如果是获取atbtoken阶段，可以使用无头模式
    if HEADLESS:
        options.add_argument('--headless')
    
    # ... (添加大量优化参数，使浏览器行为更像真人，并提升性能)
    
    try:
        # 启动浏览器
        _global_driver = uc.Chrome(options=options, use_subprocess=True)
        # ...
        
        # 检查持久化配置文件的登录状态，如果有效，直接访问购物袋页面
        if use_persistent_profile and has_existing_login:
            _global_driver.get("https://www.apple.com.cn/shop/bag")
            # ... (检查页面，如果确认已登录)
            if is_logged_in:
                logging.info("✅ 持久化配置文件登录状态有效，成功跳过2FA验证！")
                # 直接从浏览器获取as_atb cookie并返回
                # ...
                return { ... }
        
        # 如果没有持久化配置，但允许使用保存的cookies文件
        elif use_saved_cookies and not use_persistent_profile:
            # 加载cookies文件并检查有效性
            saved_cookies = load_cookies_from_file()
            if saved_cookies and is_cookies_valid(saved_cookies):
                # 将cookies添加到浏览器中，并访问购物袋页面验证
                # ...
                if not is_logged_out:
                    # 如果验证成功，直接获取as_atb cookie并返回
                    # ...
                    return { ... }
        
        # 如果以上方法都无效，则直接访问产品页面
        logging.info("直接访问产品页以获取 as_atb cookie (eager模式)...")
        _global_driver.get(product_url)
        
        # 高频轮询，直到获取到名为'as_atb'的cookie
        atb_cookie = None
        for i in range(max_retries):
            atb_cookie = _global_driver.get_cookie("as_atb")
            if atb_cookie:
                break
            time.sleep(0.3)
        
        # 如果最终没获取到，则抛出异常
        if not atb_cookie:
            raise TimeoutException("无法获取as_atb cookie")
        
        # 获取所有cookies和浏览器User-Agent
        all_cookies = _global_driver.get_cookies()
        cookie_dict = {cookie['name']: cookie['value'] for cookie in all_cookies}
        
        # 返回包含所有需要信息的结果
        return {
            'atb_cookie': atb_cookie.get('value'),
            'all_cookies': cookie_dict,
            'user_agent': _global_driver.execute_script("return navigator.userAgent;")
        }
    except Exception as e:
        # ... (错误处理)
        return None

# 定义使用后端API请求将商品添加到购物车的函数
def add_to_cart_backend(sku_info: dict, token_info: dict, expected_item_count: int):
    """使用后台请求添加商品到购物车，并通过API验证结果。"""
    # 从之前获取的token信息中提取atbtoken
    full_atb_cookie = token_info.get('atb_cookie')
    atb_token_hash = full_atb_cookie.split('|')[2]
    
    # 构建添加到购物车的POST请求所需的数据
    data = {
        'product': sku_info['sku'] + '/A',
        'atbtoken': atb_token_hash,
        'quantity': 1,
        # ... (其他表单字段)
    }
    
    # 构建请求头
    headers = { ... }
    
    try:
        # 创建一个requests会话(Session)，可以保持cookies
        session = requests.Session()
        # ... (配置重试策略)
        # 将浏览器获取的所有cookies设置到session中
        for cookie_name, cookie_value in token_info.get('all_cookies', {}).items():
            session.cookies.set(cookie_name, cookie_value)

        # 步骤1: 发送添加购物车的POST请求
        add_response = session.post(post_url, data=data, headers=headers, ...)
        
        # 检查响应是否成功
        if not (add_response.status_code == 200 and "step=attach" in add_response.url):
            return False

        # 步骤2: 访问状态API，验证购物车中的商品数量
        status_url = "https://www.apple.com.cn/shop/bag/status?apikey=..."
        status_response = session.get(status_url, headers=headers, ...)
        cart_data = status_response.json()
        
        # 比较API返回的数量和期望的数量
        item_count = cart_data.get('items', 0)
        if item_count == expected_item_count:
            return True
        else:
            return False
    except Exception as e:
        # ... (错误处理)
        return False

# 定义在浏览器中获取结账所需数据的函数
def get_checkout_data_in_browser(driver):
    """在浏览器环境中获取 STK 和 商品ID。"""
    logger.info("在浏览器环境中获取 STK 和 商品ID...")
    stk, item_ids = None, []
    try:
        # 导航到购物袋页面
        driver.get("https://www.apple.com.cn/shop/bag")
        # ...
        # 从页面的script#init_data中解析出STK令牌和商品ID
        script_json = driver.execute_script(...)
        if script_json:
            parsed = json.loads(script_json)
            # ... (解析JSON的逻辑)
            stk = ...
            item_ids = ...
        
        # 返回获取到的数据
        return stk, item_ids
    except Exception as e:
        # ... (错误处理)
        return None, []
```
