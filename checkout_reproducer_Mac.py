
import requests
import json
import re
import logging
import random
import string
import time
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import os
import json as _json_for_creds
import datetime
import hashlib
import hmac
import secrets
import base64
from urllib.parse import urlparse, parse_qs, unquote
# 尝试按需导入 Tkinter，用于2FA输入弹窗
try:
	import tkinter as _tk
	from tkinter import simpledialog as _simpledialog, messagebox as _messagebox
	_TK_AVAILABLE = True
except Exception:
	_TK_AVAILABLE = False

# --- 配置区 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 购买数量
PURCHASE_QUANTITY = 2

# 是否无头运行浏览器（结账登录时会自动弹出可见浏览器）
HEADLESS = True

# 优先使用API登录；如为True则不使用浏览器登录兜底
PREFER_API_LOGIN = True
DISABLE_BROWSER_LOGIN_FALLBACK = True
STOP_AFTER_FIRST_2FA = False
MAX_302_RETRIES = 1

# 动态记录IDMSA返回的关键头部，供后续请求携带
_IDMSA_HEADER_HINTS: dict = {}

# 是否允许使用GUI弹窗输入2FA验证码
USE_UI_2FA_PROMPT = True
# 是否在IDMSA 5xx时将请求/响应关键信息写入文件（敏感字段请自行清理）
DEBUG_IDMSA_DUMP = True

# SRP 常量（RFC 5054 2048位组，g=2，SHA-256）
# 登陆自动操作:账号 密码,并验证是否存在双重认证,若存在自动处理
def wait_for_page_load(driver, timeout=30):
    try:
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script('return document.readyState') == 'complete'
        )
    except Exception:
        logger.warning("等待页面加载完成超时。")

def handle_two_factor_auth(driver, account, log_message):
    try:
        log_message("ℹ️ 开始处理双重认证...")
        code = _prompt_2fa_code_ui()
        if not code or len(code) != 6:
            log_message("❌ 未提供有效6位验证码。")
            return False
        
        log_message("⌨️ 输入2FA验证码...")
        # Using the user's robust selector
        code_inputs = driver.find_elements(By.CSS_SELECTOR, "input.form-security-code-input")
        if not code_inputs:
            # Fallback to my previous selector just in case
            code_inputs = driver.find_elements(By.CSS_SELECTOR, "div.verify-code-input input")

        for i, input_field in enumerate(code_inputs):
            input_field.send_keys(code[i])
            time.sleep(0.1)
        log_message("✅ 2FA验证码输入完毕。")
        return True
    except Exception as e:
        log_message(f"❌ 处理双重认证时出错: {e}")
        return False

def handle_trust_browser(driver, log_message):
    try:
        log_message("🔄 正在查找“信任浏览器”按钮...")
        # Use a more specific XPath and a longer timeout
        trust_button = WebDriverWait(driver, 15).until(
            EC.element_to_be_clickable((By.XPATH, "//button[normalize-space()='信任']"))
        )
        log_message("✅ 找到“信任”按钮，正在点击...")
        driver.execute_script("arguments[0].click();", trust_button)
        log_message("✅ 已点击信任浏览器。")
    except Exception:
        log_message("ℹ️ 未找到“信任浏览器”按钮，或无需点击。")

def auto_login(driver, account, log_message, login_url):
    iframes = None
    try:
        log_message(f"导航到登录页: {login_url}")
        driver.get(login_url)
        log_message(f"页面标题: {driver.title}")

        # On the secure checkout login page, we can skip these steps
        if not ("secure" in login_url and "signIn" in login_url):
            handle_privacy_popup(driver, log_message)

            # Save artifacts before waiting for iframe
            ts = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            pre_iframe_screenshot = f"pre_iframe_wait_{ts}.png"
            pre_iframe_html = f"pre_iframe_wait_{ts}.html"
            driver.save_screenshot(pre_iframe_screenshot)
            with open(pre_iframe_html, "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            log_message(f"已保存iframe加载前的调试文件: {pre_iframe_screenshot}, {pre_iframe_html}")

        # 检查是否需要iframe（2FA登录）还是直接在主页面操作（普通登录）
        log_message("🔍 检测登录页面类型...")

        # 先检查主页面是否有登录表单
        main_page_login_selectors = [
            "#account_name_text_field",  # Apple ID输入框
            "input[type='email']",       # 邮箱输入框
            "input[name='accountName']", # 账户名输入框
            ".form-textbox-text"         # 通用文本框
        ]

        has_main_page_login = False
        for selector in main_page_login_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements and elements[0].is_displayed():
                    has_main_page_login = True
                    log_message(f"✅ 在主页面发现登录表单: {selector}")
                    break
            except:
                continue

        if not has_main_page_login:
            # 尝试切换到iframe
            log_message("⏳ 主页面无登录表单，等待并切换到登录iframe...")
            try:
                WebDriverWait(driver, 20).until(
                    EC.frame_to_be_available_and_switch_to_it((By.TAG_NAME, "iframe"))
                )
                log_message("✅ 已切换到iframe")
            except TimeoutException:
                log_message("❌ 未在20秒内检测到iframe，且主页面也无登录表单")
                return False
        else:
            log_message("✅ 使用主页面登录表单")

        # 邮箱输入 - 尝试多种选择器
        email_selectors = [
            "#account_name_text_field",
            "input[type='email']",
            "input[name='accountName']",
            ".form-textbox-text",
            "input[placeholder*='Apple ID']",
            "input[placeholder*='邮箱']"
        ]

        email_input = None
        for selector in email_selectors:
            try:
                email_input = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                if email_input.is_displayed():
                    log_message(f"✅ 找到邮箱输入框: {selector}")
                    break
            except:
                continue

        if not email_input:
            log_message("❌ 未找到邮箱输入框")
            return False
        email_input.clear()
        email_input.send_keys(account["email"])
        log_message("✅ 已输入邮箱")

        # 查找继续按钮 - 尝试多种选择器
        continue_selectors = [
            "#sign-in",
            "button[type='submit']",
            ".button-primary",
            "button:contains('继续')",
            "button:contains('Continue')",
            ".form-button",
            "input[type='submit']"
        ]

        continue_button = None
        for selector in continue_selectors:
            try:
                continue_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
                if continue_button.is_displayed():
                    log_message(f"✅ 找到继续按钮: {selector}")
                    break
            except:
                continue

        if not continue_button:
            log_message("❌ 未找到继续按钮")
            return False

        driver.execute_script("arguments[0].click();", continue_button)
        log_message("✅ 已点击继续按钮")

        try:
            # 查找密码输入框 - 尝试多种选择器
            password_selectors = [
                "#password_text_field",
                "input[type='password']",
                "input[name='password']",
                ".form-textbox-password",
                "input[placeholder*='密码']",
                "input[placeholder*='Password']"
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    password_input = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    if password_input.is_displayed():
                        log_message(f"✅ 找到密码输入框: {selector}")
                        break
                except:
                    continue

            if not password_input:
                log_message("❌ 未找到密码输入框")
                return False
            driver.execute_script("arguments[0].scrollIntoView(true);", password_input)
            password_input.clear()
            password_input.send_keys(account["password"])
            log_message("✅ 已输入密码")

            # 查找登录按钮 - 尝试多种选择器
            signin_selectors = [
                "#sign-in",
                "button[type='submit']",
                ".button-primary",
                "button:contains('登录')",
                "button:contains('Sign In')",
                ".form-button",
                "input[type='submit']",
                "button:contains('继续')",
                "button:contains('Continue')"
            ]

            sign_in_button = None
            for selector in signin_selectors:
                try:
                    sign_in_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    if sign_in_button.is_displayed():
                        log_message(f"✅ 找到登录按钮: {selector}")
                        break
                except:
                    continue

            if not sign_in_button:
                log_message("❌ 未找到登录按钮")
                return False

            driver.execute_script("arguments[0].scrollIntoView(true);", sign_in_button)
            driver.execute_script("arguments[0].click();", sign_in_button)
            log_message("✅ 已点击登录按钮")

            # 智能检测：2FA、普通登录成功或页面跳转
            old_url = driver.current_url
            two_fa_handled = False

            log_message("🔍 智能检测登录结果：2FA界面、登录成功或页面跳转...")

            try:
                # 使用WebDriverWait同时等待多个条件
                def check_login_result(driver):
                    current_url = driver.current_url
                    page_source = driver.page_source

                    # 检查URL是否变化（登录成功跳转）
                    if current_url != old_url:
                        # 进一步检查是否真的登录成功
                        if ("checkout" in current_url and "signin" not in current_url.lower()) or \
                           "订单选项" in page_source or \
                           "secure" in current_url:
                            return "login_success"
                        else:
                            return "url_changed"

                    # 检查是否有错误信息（登录失败）
                    error_indicators = [
                        "密码不正确", "账户被锁定", "登录失败",
                        "incorrect password", "account locked", "login failed",
                        "验证失败", "authentication failed"
                    ]
                    if any(error in page_source.lower() for error in error_indicators):
                        return "login_failed"

                    # 检查双重认证输入框（在iframe中或主页面中）
                    try:
                        # 先检查主页面
                        code_inputs = driver.find_elements(By.CSS_SELECTOR, "input.form-security-code-input")
                        if len(code_inputs) >= 6:
                            # 验证页面文本包含双重认证关键词
                            keywords = ["双重认证", "验证码", "安全验证", "two-factor", "verification"]
                            if any(k in page_source.lower() for k in keywords):
                                return "2fa_detected_main"

                        # 检查iframe中的双重认证
                        iframes = driver.find_elements(By.TAG_NAME, "iframe")
                        if iframes:
                            current_frame = None
                            try:
                                # 保存当前frame状态
                                try:
                                    current_frame = driver.execute_script("return window.frameElement")
                                except:
                                    current_frame = None

                                driver.switch_to.frame(iframes[0])
                                code_inputs = driver.find_elements(By.CSS_SELECTOR, "input.form-security-code-input")
                                if len(code_inputs) >= 6:
                                    iframe_source = driver.page_source
                                    keywords = ["双重认证", "验证码", "安全验证", "two-factor", "verification"]
                                    if any(k in iframe_source.lower() for k in keywords):
                                        return "2fa_detected_iframe"

                            except Exception:
                                pass
                            finally:
                                # 恢复到原来的frame
                                try:
                                    if current_frame is None:
                                        driver.switch_to.default_content()
                                    else:
                                        driver.switch_to.frame(current_frame)
                                except:
                                    driver.switch_to.default_content()
                    except Exception:
                        pass

                    return False

                # 智能等待：最多等待20秒，每0.5秒检查一次
                result = WebDriverWait(driver, 20).until(check_login_result)

                if result == "login_success":
                    log_message("✅ 智能检测：登录成功，已跳转到结账页面")
                    return True
                elif result == "url_changed":
                    log_message("✅ 智能检测：登录后URL已变化，无需双重认证")
                    # 普通登录成功，直接返回True
                    return True
                elif result == "login_failed":
                    log_message("❌ 智能检测：登录失败，请检查账号密码")
                    return False
                elif result in ["2fa_detected_main", "2fa_detected_iframe"]:
                    location = "主页面" if result == "2fa_detected_main" else "iframe"
                    log_message(f"✅ 智能检测：在{location}中发现双重认证界面，立即处理...")

                    # 立即处理双重认证
                    two_fa_result = handle_two_factor_auth(driver, account, log_message)
                    if not two_fa_result:
                        log_message("❌ 双重认证处理失败")
                        return False
                    handle_trust_browser(driver, log_message)
                    two_fa_handled = True

            except Exception as e:
                log_message(f"⚠️ 智能检测超时或出错，使用备用检测方案: {str(e)}")
                # 备用方案：直接尝试处理双重认证
                two_fa_result = handle_two_factor_auth(driver, account, log_message)
                if two_fa_result:
                    handle_trust_browser(driver, log_message)
                    two_fa_handled = True
                else:
                    log_message("⚠️ 备用检测也未发现双重认证，可能登录成功或存在其他问题")

            # 如果处理了双重认证，等待页面跳转
            if two_fa_handled:
                max_retries = 5
                for attempt in range(max_retries):
                    if driver.current_url != old_url:
                        log_message(f"✅ 双重认证处理后URL已变化 (第{attempt+1}次检测)")
                        break
                    else:
                        if attempt < max_retries - 1:
                            log_message(f"⏳ 等待双重认证后页面跳转... (第{attempt+1}次重试)")
                            time.sleep(2)
                        else:
                            log_message(f"❌ 双重认证处理后未检测到页面跳转")
                            driver.save_screenshot("error_after_2fa.png")
                            if iframes:
                                driver.switch_to.default_content()
                            return False

        except Exception as e:
            log_message(f"❌ 密码输入或登录按钮点击失败：{str(e)}")
            driver.save_screenshot("error_password_input.png")
            # Switch back before returning
            try:
                driver.switch_to.default_content()
            except: pass
            return False
        
        # Switch back to main frame at the end
        driver.switch_to.default_content()
        log_message("✅ 已切回主frame")
        
        # 登录成功后保存cookies
        try:
            all_cookies = driver.get_cookies()
            if save_cookies_to_file(all_cookies):
                log_message("✅ 已保存登录cookies，下次可跳过2FA验证")
        except Exception as e:
            log_message(f"⚠️ 保存cookies失败: {e}")
        
        return True
    except Exception as e:
        log_message(f"❌ 普通登录页面自动填充失败: {repr(e)}")
        driver.save_screenshot("error_auto_login.png")
        try:
            driver.switch_to.default_content()
        except:
            pass
        return False

# 隐私弹窗处理    
def handle_privacy_popup(driver, log_message):
    """处理隐私弹窗"""
    try:
        log_message("🔄 检查并处理隐私弹窗...")
        wait_for_page_load(driver, 10)
        # 智能等待隐私弹窗出现，而不是固定等待3秒
        try:
            WebDriverWait(driver, 5).until(
                lambda d: d.find_element(By.CSS_SELECTOR, "div.rc-overlay.rs-signin-consent-overlay") or 
                         "Apple 和你的数据隐私" in d.page_source
            )
        except:
            # 如果没有检测到隐私弹窗，使用最小等待时间
            time.sleep(0.5)
        privacy_detected = False
        try:
            privacy_overlay = driver.find_element(By.CSS_SELECTOR, "div.rc-overlay.rs-signin-consent-overlay")
            if privacy_overlay:
                log_message("ℹ️ 通过CSS选择器检测到隐私弹窗")
                privacy_detected = True
        except:
            log_message("ℹ️ CSS选择器未检测到隐私弹窗")
        if not privacy_detected:
            try:
                privacy_title = driver.find_element(By.XPATH, "//h2[contains(text(), 'Apple') and contains(text(), '隐私')] ")
                if privacy_title:
                    log_message("ℹ️ 通过标题文本检测到隐私弹窗")
                    privacy_detected = True
            except:
                log_message("ℹ️ 标题文本未检测到隐私弹窗")
        if not privacy_detected:
            try:
                iframes = driver.find_elements(By.TAG_NAME, "iframe")
                for iframe in iframes:
                    try:
                        driver.switch_to.frame(iframe)
                        privacy_title = driver.find_elements(By.XPATH, "//h2[contains(text(), 'Apple') and contains(text(), '隐私')] ")
                        if privacy_title:
                            log_message("ℹ️ 在iframe中检测到隐私弹窗")
                            privacy_detected = True
                            break
                        driver.switch_to.default_content()
                    except:
                        driver.switch_to.default_content()
                        continue
            except:
                log_message("ℹ️ iframe检测未发现隐私弹窗")
        if not privacy_detected:
            try:
                page_source = driver.page_source
                if "rs-signin-consent-overlay" in page_source and "Apple 和你的数据隐私" in page_source:
                    log_message("ℹ️ 在页面源码中检测到隐私弹窗")
                    privacy_detected = True
            except:
                log_message("ℹ️ 页面源码检测失败")
        if privacy_detected:
            log_message("ℹ️ 检测到隐私弹窗，开始处理...")
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.rc-overlay.rs-signin-consent-overlay"))
                )
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.r-fade-transition-enter-done"))
                )
                WebDriverWait(driver, 10).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "h2.rs-signin-consent-overlay-header"))
                )
                privacy_policy_checkbox = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.ID, "signIn-guestLogin-consentOverlay-policiesAccepted"))
                )
                driver.execute_script("arguments[0].click();", privacy_policy_checkbox)
                data_handling_checkbox = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.ID, "signIn-guestLogin-consentOverlay-dataOutSideMyCountry"))
                )
                driver.execute_script("arguments[0].click();", data_handling_checkbox)
                agree_and_continue_button_privacy = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[text()='同意并继续']"))
                )
                driver.execute_script("arguments[0].click();", agree_and_continue_button_privacy)
                WebDriverWait(driver, 10).until_not(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.rc-overlay.rs-signin-consent-overlay"))
                )
                log_message("✅ 成功处理隐私弹窗")
            except Exception as e:
                log_message(f"ℹ️ 未找到隐私弹窗内容或处理失败：{str(e)}")
                try:
                    overlay = driver.find_element(By.CSS_SELECTOR, "div[data-core-overlay-cover]")
                    driver.execute_script("arguments[0].style.display = 'none';", overlay)
                except:
                    pass
        else:
            log_message("ℹ️ 无需处理隐私弹窗，继续执行后续步骤")
    except Exception as e:
        log_message(f"ℹ️ 处理隐私弹窗时发生错误：{str(e)}")
    finally:
        try:
            driver.switch_to.default_content()
        except:
            pass


_SRP_g = 2


def _sha256(data: bytes) -> bytes:
	return hashlib.sha256(data).digest()


def _pad_bytes(num: int, pad_len: int = 256) -> bytes:
	b = num.to_bytes((num.bit_length() + 7) // 8, 'big')
	if len(b) < pad_len:
		b = (b"\x00" * (pad_len - len(b))) + b
	return b


def _bytes_xor(a: bytes, b: bytes) -> bytes:
	return bytes(x ^ y for x, y in zip(a, b))


def _b64_of_int_padded(num: int) -> str:
	return base64.b64encode(_pad_bytes(num, 256)).decode('ascii')





import os
import json

# 定义全局变量
APPLE_ID = None
APPLE_PWD = None

def _load_credentials_from_file():
    try:
        # 获取当前脚本所在目录
        base_dir = os.path.dirname(os.path.abspath(__file__))
        cred_path = os.path.join(base_dir, 'credentials.json')
        
        if os.path.exists(cred_path):
            with open(cred_path, 'r', encoding='utf-8') as f:
                data = json.load(f)  # 使用标准 json 模块
                # 支持大小写和不同命名方式
                apple_id = data.get('apple_id') or data.get('APPLE_ID')
                apple_pwd = data.get('apple_pwd') or data.get('APPLE_PWD')
                return apple_id, apple_pwd
    except Exception as e:
        print(f"读取凭据文件时出错：{e}")
        return None, None
    return None, None

# 直接从文件加载凭据
APPLE_ID, APPLE_PWD = _load_credentials_from_file()

if APPLE_ID and APPLE_PWD:
    print("✅ 已从 credentials.json 成功加载 Apple ID 凭据")
else:
    print("❌ 未找到 credentials.json 或文件中缺少必要的凭据（apple_id 和 apple_pwd）")
    # 可根据需要抛出异常或退出程序
    # raise Exception("缺少登录凭据，无法继续执行。")

	
# 手动设置atbtoken（如果自动获取失败，可以手动设置）
MANUAL_ATBTOKEN = None  # 例如: "your_atbtoken_here"

# 全局浏览器实例，避免重复启动
_global_driver = None

# Cookies保存路径
COOKIES_FILE = "apple_cookies.json"

# Chrome用户配置文件目录
CHROME_PROFILE_DIR = "chrome_profile_apple"

def save_cookies_to_file(cookies, filename=COOKIES_FILE):
    """保存cookies到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(cookies, f, ensure_ascii=False, indent=2)
        logger.info(f"✅ 已保存 {len(cookies)} 个cookies到 {filename}")
        return True
    except Exception as e:
        logger.error(f"保存cookies失败: {e}")
        return False

def setup_chrome_profile_directory():
    """设置Chrome用户配置文件目录"""
    try:
        # 获取当前脚本所在目录
        base_dir = os.path.dirname(os.path.abspath(__file__))
        profile_path = os.path.join(base_dir, CHROME_PROFILE_DIR)

        # 如果目录不存在，创建它
        if not os.path.exists(profile_path):
            os.makedirs(profile_path)
            logger.info(f"✅ 已创建Chrome配置文件目录: {profile_path}")
        else:
            logger.info(f"✅ Chrome配置文件目录已存在: {profile_path}")

        return profile_path
    except Exception as e:
        logger.error(f"设置Chrome配置文件目录失败: {e}")
        return None

def check_profile_login_state(profile_path):
    """检查Chrome配置文件中是否已有Apple ID登录状态"""
    try:
        # 检查关键的登录状态文件
        login_data_file = os.path.join(profile_path, "Default", "Login Data")
        preferences_file = os.path.join(profile_path, "Default", "Preferences")

        if os.path.exists(login_data_file) and os.path.exists(preferences_file):
            # 检查文件大小，如果太小说明可能是空的
            login_data_size = os.path.getsize(login_data_file)
            preferences_size = os.path.getsize(preferences_file)

            if login_data_size > 1024 and preferences_size > 1024:  # 至少1KB
                logger.info(f"✅ 检测到已有登录数据 (Login Data: {login_data_size} bytes, Preferences: {preferences_size} bytes)")
                return True

        logger.info("❌ 未检测到有效的登录数据")
        return False
    except Exception as e:
        logger.error(f"检查配置文件登录状态失败: {e}")
        return False

def perform_first_time_login(driver):
    """执行首次登录，包括2FA验证，并保存到持久化配置文件"""
    try:
        logger.info("开始首次登录流程...")

        # 访问Apple登录页面
        login_url = "https://appleid.apple.com.cn/sign-in"
        logger.info(f"导航到登录页面: {login_url}")
        driver.get(login_url)
        time.sleep(3)

        # 使用现有的auto_login函数进行登录
        account = {"email": APPLE_ID, "password": APPLE_PWD}
        log_func = lambda msg: logger.info(f"[首次登录] {msg}")

        # 调用现有的登录函数
        login_success = auto_login(driver, account, log_func, login_url)

        if login_success:
            logger.info("✅ 首次登录成功，登录状态已保存到持久化配置文件")

            # 验证登录状态
            driver.get("https://www.apple.com.cn/shop/bag")
            time.sleep(2)

            page_source = driver.page_source
            if 'id="ac-gn-signout"' in page_source or "购物袋" in page_source:
                logger.info("✅ 登录状态验证成功")
                return True
            else:
                logger.warning("⚠️ 登录状态验证失败")
                return False
        else:
            logger.error("❌ 首次登录失败")
            return False

    except Exception as e:
        logger.error(f"首次登录过程中出错: {e}")
        return False

def auto_login_with_persistent_profile(driver, account, log_message, login_url):
    """
    在结账时使用持久化配置文件进行登录
    如果已有登录状态则跳过2FA，否则进行完整登录流程
    """
    try:
        log_message("🔄 检查持久化配置文件中的登录状态...")

        # 先检查当前是否已经登录
        current_url = driver.current_url
        page_source = driver.page_source

        log_message(f"当前页面URL: {current_url}")
        log_message(f"当前页面标题: {driver.title}")

        # 更精确地检查是否已经在结账页面
        is_checkout_page = (
            ("checkout" in current_url and "signin" not in current_url.lower()) and
            "订单选项" in page_source and
            "signin" not in current_url.lower() and
            "signIn" not in current_url
        )

        # 检查是否在登录页面
        is_login_page = (
            "signin" in current_url.lower() or
            "signIn" in current_url or
            "登录" in driver.title or
            "使用你的 Apple 账户结账" in page_source or
            "Apple ID" in page_source
        )

        if is_checkout_page:
            log_message("✅ 检测到已在结账页面，说明登录状态有效，无需重新登录")
            return True
        elif is_login_page:
            log_message("🔐 检测到登录页面，需要进行登录")
            # 直接执行登录流程，不再检查其他指标
            login_success = auto_login(driver, account, log_message, login_url)

            if login_success:
                log_message("✅ 登录成功，状态已保存到持久化配置文件")
                return True
            else:
                log_message("❌ 登录失败")
                return False
        else:
            log_message("❓ 页面状态不明确，检查其他登录指标...")

            # 检查其他登录状态指标
            other_login_indicators = [
                'id="ac-gn-signout"' in page_source,
                "购物袋" in page_source,
                "/shop/bag" in current_url
            ]

            if any(other_login_indicators):
                log_message("✅ 检测到已登录状态，无需重新登录")
                return True

            # 如果没有登录状态，使用现有的auto_login函数
            log_message("❌ 未检测到登录状态，开始登录流程...")

            # 由于使用了持久化配置文件，登录成功后状态会自动保存
            login_success = auto_login(driver, account, log_message, login_url)

        if login_success:
            log_message("✅ 登录成功，状态已保存到持久化配置文件")
            return True
        else:
            log_message("❌ 登录失败")
            return False

    except Exception as e:
        log_message(f"❌ 持久化配置文件登录过程中出错: {e}")
        return False

def get_current_stk_from_page(driver):
    """从当前页面获取最新的STK token"""
    try:
        # 方法1: 从init_data脚本节点解析STK
        script_json = driver.execute_script("var e=document.getElementById('init_data'); return e? e.textContent : null;")
        if script_json:
            try:
                parsed = json.loads(script_json)
                meta = parsed.get('meta', {})
                h = meta.get('h', {})
                stk = h.get('x-aos-stk')
                if stk:
                    logger.info(f"✅ 从页面获取到最新STK: {stk}")
                    return stk
            except Exception:
                pass

        # 方法2: 从window.init_data获取
        init_data = driver.execute_script("return window.init_data;")
        if isinstance(init_data, dict):
            meta = init_data.get('meta', {})
            h = meta.get('h', {})
            stk = h.get('x-aos-stk')
            if stk:
                logger.info(f"✅ 从window.init_data获取到STK: {stk}")
                return stk

        return None
    except Exception as e:
        logger.warning(f"获取页面STK失败: {e}")
        return None

def try_ui_interaction_for_fulfillment(driver):
    """
    尝试UI交互，确保默认选项被选中
    通常情况下会自动默认选择第一个选项，但有时需要手动点击
    """
    try:
        logger.info("🖱️ 尝试点击默认的配送选项...")

        # 等待页面加载完成
        time.sleep(2)

        # 尝试多种选择器来找到默认的配送选项
        fulfillment_selectors = [
            # 配送到家选项
            "input[value='HOME']",
            "input[name*='selectFulfillmentLocation'][value='HOME']",
            "input[id*='fulfillment'][value='HOME']",
            # 通用的第一个单选按钮
            "input[type='radio'][name*='fulfillment']:first-of-type",
            "input[type='radio'][name*='selectFulfillmentLocation']:first-of-type",
            # 基于文本的选择器
            "label:contains('送货')",
            "label:contains('配送到家')",
        ]

        fulfillment_clicked = False
        for selector in fulfillment_selectors:
            try:
                if ":contains(" in selector:
                    # 使用XPath处理包含文本的选择器
                    xpath_selector = f"//label[contains(text(), '送货') or contains(text(), '配送到家')]"
                    elements = driver.find_elements(By.XPATH, xpath_selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                if elements:
                    element = elements[0]
                    if element.is_displayed() and element.is_enabled():
                        driver.execute_script("arguments[0].click();", element)
                        logger.info(f"✅ 成功点击配送选项: {selector}")
                        fulfillment_clicked = True
                        break
            except Exception as e:
                logger.debug(f"尝试选择器 {selector} 失败: {e}")
                continue

        if not fulfillment_clicked:
            logger.warning("⚠️ 未找到可点击的配送选项")

        # 尝试点击"继续填写送货地址"或类似的按钮
        continue_selectors = [
            "button:contains('继续')",
            "button:contains('继续填写')",
            "button:contains('下一步')",
            "button[type='submit']",
            ".button-primary",
            ".continue-button",
            "input[type='submit']"
        ]

        continue_clicked = False
        for selector in continue_selectors:
            try:
                if ":contains(" in selector:
                    # 使用XPath处理包含文本的选择器
                    xpath_selector = f"//button[contains(text(), '继续') or contains(text(), '下一步')]"
                    elements = driver.find_elements(By.XPATH, xpath_selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                if elements:
                    element = elements[0]
                    if element.is_displayed() and element.is_enabled():
                        driver.execute_script("arguments[0].click();", element)
                        logger.info(f"✅ 成功点击继续按钮: {selector}")
                        continue_clicked = True
                        break
            except Exception as e:
                logger.debug(f"尝试继续按钮选择器 {selector} 失败: {e}")
                continue

        if not continue_clicked:
            logger.warning("⚠️ 未找到可点击的继续按钮")

        # 如果至少有一个操作成功，返回True
        return fulfillment_clicked or continue_clicked

    except Exception as e:
        logger.warning(f"UI交互失败: {e}")
        return False

def get_comprehensive_form_data(driver):
    """
    使用多种方法获取表单数据
    """
    try:
        logger.info("📋 尝试获取页面表单数据...")

        # 等待页面完全加载
        time.sleep(1)

        # 方法1: 获取所有checkout.fulfillment相关的表单字段
        form_data = {}

        try:
            form_data_script = """
                var data = {};

                // 获取所有input字段
                var inputs = document.querySelectorAll('input[name*="checkout.fulfillment"], input[name*="fulfillment"]');
                for (var i = 0; i < inputs.length; i++) {
                    var input = inputs[i];
                    if (input.name) {
                        var value = '';
                        if (input.type === 'radio' || input.type === 'checkbox') {
                            if (input.checked) {
                                value = input.value || 'on';
                            }
                        } else {
                            value = input.value || input.defaultValue || '';
                        }
                        if (value) {
                            data[input.name] = value;
                        }
                    }
                }

                // 获取所有select字段
                var selects = document.querySelectorAll('select[name*="checkout.fulfillment"], select[name*="fulfillment"]');
                for (var i = 0; i < selects.length; i++) {
                    var select = selects[i];
                    if (select.name && select.value) {
                        data[select.name] = select.value;
                    }
                }

                // 获取所有textarea字段
                var textareas = document.querySelectorAll('textarea[name*="checkout.fulfillment"], textarea[name*="fulfillment"]');
                for (var i = 0; i < textareas.length; i++) {
                    var textarea = textareas[i];
                    if (textarea.name && textarea.value) {
                        data[textarea.name] = textarea.value;
                    }
                }

                return data;
            """

            form_data = driver.execute_script(form_data_script)
            logger.info(f"方法1获取到 {len(form_data)} 个表单字段")

        except Exception as e:
            logger.warning(f"方法1获取表单数据失败: {e}")

        # 方法2: 如果方法1没有获取到数据，尝试更广泛的搜索
        if not form_data:
            try:
                broader_script = """
                    var data = {};

                    // 获取所有可能相关的input字段
                    var allInputs = document.querySelectorAll('input[name], select[name], textarea[name]');
                    for (var i = 0; i < allInputs.length; i++) {
                        var element = allInputs[i];
                        var name = element.name;

                        // 只获取与checkout或fulfillment相关的字段
                        if (name && (name.includes('checkout') || name.includes('fulfillment') ||
                                   name.includes('delivery') || name.includes('shipping') ||
                                   name.includes('address'))) {
                            var value = '';
                            if (element.type === 'radio' || element.type === 'checkbox') {
                                if (element.checked) {
                                    value = element.value || 'on';
                                }
                            } else {
                                value = element.value || element.defaultValue || '';
                            }
                            if (value) {
                                data[name] = value;
                            }
                        }
                    }

                    return data;
                """

                form_data = driver.execute_script(broader_script)
                logger.info(f"方法2获取到 {len(form_data)} 个表单字段")

            except Exception as e:
                logger.warning(f"方法2获取表单数据失败: {e}")

        return form_data

    except Exception as e:
        logger.warning(f"获取表单数据失败: {e}")
        return {}

def extract_form_data_from_init_data(driver):
    """
    从页面的init_data JSON中提取表单数据
    """
    try:
        logger.info("📋 尝试从init_data解析表单数据...")

        # 获取init_data
        script_json = driver.execute_script("var e=document.getElementById('init_data'); return e? e.textContent : null;")
        if not script_json:
            logger.warning("未找到init_data脚本")
            return {}

        try:
            parsed = json.loads(script_json)
            logger.info("✅ 成功解析init_data JSON")

            # 从JSON中提取表单相关数据
            form_data = {}

            # 查找checkout相关数据
            if 'checkout' in parsed:
                checkout_data = parsed['checkout']

                # 递归提取所有相关字段
                def extract_fields(data, prefix="checkout"):
                    if isinstance(data, dict):
                        for key, value in data.items():
                            new_prefix = f"{prefix}.{key}"
                            if isinstance(value, (dict, list)):
                                extract_fields(value, new_prefix)
                            else:
                                # 只保留字符串和数字值
                                if isinstance(value, (str, int, float, bool)):
                                    form_data[new_prefix] = str(value)
                    elif isinstance(data, list):
                        for i, item in enumerate(data):
                            new_prefix = f"{prefix}-{i+1}"
                            extract_fields(item, new_prefix)

                extract_fields(checkout_data, "checkout")

            logger.info(f"从init_data提取到 {len(form_data)} 个字段")
            return form_data

        except json.JSONDecodeError as e:
            logger.warning(f"解析init_data JSON失败: {e}")
            return {}

    except Exception as e:
        logger.warning(f"从init_data提取表单数据失败: {e}")
        return {}

def build_complete_fulfillment_data(form_data, driver):
    """
    构建完整的fulfillment POST数据
    """
    try:
        logger.info("🔧 构建完整的fulfillment数据...")

        # 从页面获取当前地址信息
        address_info = extract_address_from_page(driver)
        city = address_info.get('city', '深圳')
        state = address_info.get('state', '广东')
        district = address_info.get('district', '宝安区')
        province_city_district = f"{state} {city} {district}"

        # 开始构建数据
        fulfillment_data = {}

        # 如果从页面获取到了数据，优先使用
        if form_data:
            fulfillment_data.update(form_data)
            logger.info(f"使用页面获取的 {len(form_data)} 个字段")

        # 确保必要的字段存在
        essential_fields = {
            "checkout.fulfillment.fulfillmentOptions.selectFulfillmentLocation": "HOME",
            "checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.state": state,
            "checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.city": city,
            "checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.district": district,
            "checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.provinceCityDistrict": province_city_district,
            "checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.countryCode": "CN",
        }

        # 只添加页面中没有的字段
        for key, value in essential_fields.items():
            if key not in fulfillment_data:
                fulfillment_data[key] = value

        # 确保有配送选项
        shipping_option_key = "checkout.fulfillment.deliveryTab.delivery.shipmentGroups.shipmentGroup-1.shipmentOptionsGroups.shipmentOptionsGroup-1.shippingOptions.selectShippingOption"
        if shipping_option_key not in fulfillment_data:
            fulfillment_data[shipping_option_key] = "A8"

        logger.info(f"最终构建了 {len(fulfillment_data)} 个字段的fulfillment数据")
        return fulfillment_data

    except Exception as e:
        logger.warning(f"构建fulfillment数据失败: {e}")
        return {}

def extract_address_from_page(driver):
    """从结账页面提取地址信息"""
    try:
        page_source = driver.page_source

        # 尝试从页面JSON数据中提取地址信息
        try:
            script_json = driver.execute_script("var e=document.getElementById('init_data'); return e? e.textContent : null;")
            if script_json:
                parsed = json.loads(script_json)
                # 查找地址相关数据
                if 'checkout' in parsed:
                    checkout_data = parsed['checkout']
                    # 这里可以根据实际的JSON结构来提取地址信息
                    # 暂时使用默认值
                    pass
        except Exception:
            pass

        # 从页面文本中查找地址信息
        address_patterns = {
            'state': ['广东', '北京', '上海', '浙江', '江苏', '山东', '河南', '湖北', '湖南', '四川'],
            'city': ['深圳', '广州', '北京', '上海', '杭州', '南京', '青岛', '郑州', '武汉', '长沙', '成都'],
            'district': ['宝安区', '南山区', '福田区', '罗湖区', '龙岗区', '盐田区', '龙华区', '坪山区', '光明区', '大鹏新区']
        }

        found_address = {}

        for addr_type, patterns in address_patterns.items():
            for pattern in patterns:
                if pattern in page_source:
                    found_address[addr_type] = pattern
                    break

        # 如果没有找到，使用默认地址
        default_address = {
            'state': '广东',
            'city': '深圳',
            'district': '宝安区'
        }

        result = {**default_address, **found_address}
        logger.info(f"提取到的地址信息: {result}")
        return result

    except Exception as e:
        logger.warning(f"提取地址信息失败: {e}")
        return {'state': '广东', 'city': '深圳', 'district': '宝安区'}

def handle_fulfillment_step(driver, session, stk, user_agent):
    """
    处理地址选择步骤 (fulfillment)
    改进版：先尝试点击默认选项，然后使用API请求方式
    """
    try:
        logger.info("处理地址选择步骤（改进版：UI交互 + API方式）...")

        # 保存当前页面状态用于调试
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        fulfillment_screenshot = f"fulfillment_before_{timestamp}.png"
        fulfillment_html = f"fulfillment_before_{timestamp}.html"

        try:
            driver.save_screenshot(fulfillment_screenshot)
            with open(fulfillment_html, "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            logger.info(f"✅ 已保存地址选择前状态: {fulfillment_screenshot}, {fulfillment_html}")
        except Exception as e:
            logger.warning(f"保存地址选择前状态失败: {e}")

        # 步骤1: 先尝试UI交互，确保默认选项被选中
        logger.info("🖱️ 步骤1: 尝试UI交互，确保默认选项被选中...")
        ui_interaction_success = try_ui_interaction_for_fulfillment(driver)

        if ui_interaction_success:
            logger.info("✅ UI交互成功，页面状态已更新")
            # 等待页面更新后再获取数据
            time.sleep(2)
        else:
            logger.warning("⚠️ UI交互失败，继续使用API方式")

        # 步骤2: 获取当前页面的STK（可能已更新）
        current_stk = get_current_stk_from_page(driver) or stk
        logger.info(f"✅ 从页面获取到最新STK: {current_stk}")

        # 步骤3: 尝试多种方法获取表单数据
        logger.info("📋 步骤3: 获取表单数据...")
        form_data = get_comprehensive_form_data(driver)

        if not form_data:
            logger.warning("⚠️ 无法从页面获取表单数据，尝试从init_data解析...")
            form_data = extract_form_data_from_init_data(driver)

        logger.info(f"从页面获取的fulfillment表单数据: {form_data}")

        # 步骤4: 构建API请求
        base_url = driver.current_url.split('/checkout')[0] if '/checkout' in driver.current_url else driver.current_url.split('/shop')[0] + '/shop'
        fulfillment_url = f"{base_url}/checkoutx/fulfillment"

        # 构建更真实的请求头，基于断点信息
        headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Origin": base_url,
            "Referer": driver.current_url,
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": user_agent,
            "X-Requested-With": "XMLHttpRequest",
            "modelVersion": "v2",
            "syntax": "graviton",
            "x-aos-model-page": "checkoutPage",
            "x-aos-stk": current_stk,
        }

        # 添加fetch call token
        try:
            fetch_token = generate_aos_fetch_call_token()
            headers["x-aos-ui-fetch-call-1"] = fetch_token
        except Exception as e:
            logger.warning(f"生成fetch token失败: {e}")

        # 从浏览器获取额外的headers
        try:
            browser_headers = driver.execute_script("""
                return {
                    'sec-ch-ua': navigator.userAgentData ? navigator.userAgentData.brands.map(b => '"' + b.brand + '";v="' + b.version + '"').join(', ') : '',
                    'sec-ch-ua-mobile': navigator.userAgentData ? (navigator.userAgentData.mobile ? '?1' : '?0') : '',
                    'sec-ch-ua-platform': navigator.userAgentData ? '"' + navigator.userAgentData.platform + '"' : ''
                };
            """)

            for key, value in browser_headers.items():
                if value:
                    headers[key] = value

        except Exception as e:
            logger.debug(f"获取浏览器headers失败: {e}")

        # 步骤5: 构建完整的POST数据
        fulfillment_data = build_complete_fulfillment_data(form_data, driver)

        # 添加查询参数
        params = {
            "_a": "continueFromFulfillmentToShipping",
            "_m": "checkout.fulfillment"
        }

        logger.info(f"发送地址选择API请求到: {fulfillment_url}")
        logger.info(f"请求数据字段数量: {len(fulfillment_data)}")
        logger.info(f"关键请求数据: {dict(list(fulfillment_data.items())[:5])}")  # 只显示前5个字段

        response = session.post(
            fulfillment_url,
            headers=headers,
            data=fulfillment_data,
            params=params,
            timeout=30
        )

        logger.info(f"地址选择API响应状态码: {response.status_code}")

        if response.status_code == 200:
            # 检查响应内容
            response_text = response.text
            logger.info(f"响应内容长度: {len(response_text)}")

            # 等待页面更新
            logger.info("等待页面更新...")
            time.sleep(3)

            # 检查页面是否已跳转到下一步
            try:
                current_url = driver.current_url
                logger.info(f"API调用后页面URL: {current_url}")

                if "Shipping" in current_url or "shipping" in current_url.lower():
                    logger.info("✅ 地址选择成功，页面已跳转到配送步骤")
                    return True
                elif "Fulfillment" in current_url:
                    logger.warning("⚠️ 页面仍在地址选择步骤，可能需要更多信息")
                    # 检查是否有错误信息
                    page_source = driver.page_source
                    if "错误" in page_source or "required" in page_source.lower() or "invalid" in page_source.lower():
                        logger.error("❌ 页面显示错误信息，地址选择失败")
                        return False
                    else:
                        logger.info("✅ 地址选择API调用成功（页面无错误）")
                        return True
                else:
                    logger.info("✅ 地址选择步骤完成")
                    return True
            except Exception as e:
                logger.warning(f"检查页面状态时出错: {e}")
                logger.info("✅ 地址选择API调用成功（假设成功）")
                return True
        else:
            logger.error(f"❌ 地址选择API请求失败: {response.status_code}")
            logger.error(f"响应内容: {response.text[:500]}")
            return False

    except Exception as e:
        logger.error(f"地址选择步骤出错: {e}")
        return False

def handle_shipping_step(driver, session, stk, user_agent):
    """
    处理配送方式步骤 (shipping)
    """
    try:
        logger.info("处理配送方式步骤...")

        # 获取当前页面的STK
        current_stk = get_current_stk_from_page(driver) or stk

        # 构建shipping API请求URL
        base_url = driver.current_url.split('/checkout')[0] if '/checkout' in driver.current_url else driver.current_url.split('/shop')[0] + '/shop'
        shipping_url = f"{base_url}/checkoutx/shipping"

        headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": base_url,
            "Referer": driver.current_url,
            "User-Agent": user_agent,
            "X-Requested-With": "Fetch",
            "modelVersion": "v2",
            "syntax": "graviton",
            "x-aos-model-page": "checkoutPage",
            "x-aos-stk": current_stk,
            "x-aos-ui-fetch-call-1": generate_aos_fetch_call_token()
        }

        # 构建POST数据 - 使用默认配送选项
        shipping_data = {
            "checkout.shipping.shippingTab.shippingOptions.selectShippingOption": "A8"
        }

        # 添加查询参数
        params = {
            "_a": "continueFromShippingToBilling",
            "_m": "checkout.shipping"
        }

        logger.info(f"发送配送方式API请求到: {shipping_url}")
        response = session.post(
            shipping_url,
            headers=headers,
            data=shipping_data,
            params=params,
            timeout=30
        )

        logger.info(f"配送方式API响应状态码: {response.status_code}")

        if response.status_code == 200:
            # 等待页面更新
            logger.info("等待页面更新...")
            time.sleep(3)

            # 检查页面是否已跳转到下一步
            try:
                current_url = driver.current_url
                logger.info(f"配送API调用后页面URL: {current_url}")

                if "Billing" in current_url or "billing" in current_url.lower():
                    logger.info("✅ 配送方式成功，页面已跳转到支付步骤")
                    return True
                else:
                    logger.info("✅ 配送方式步骤完成")
                    return True
            except Exception as e:
                logger.warning(f"检查页面状态时出错: {e}")
                logger.info("✅ 配送方式API调用成功")
                return True
        else:
            logger.error(f"❌ 配送方式API请求失败: {response.status_code}")
            logger.error(f"响应内容: {response.text[:500]}")
            return False

    except Exception as e:
        logger.error(f"配送方式步骤出错: {e}")
        return False

def handle_billing_step(driver, session, stk, user_agent):
    """
    处理支付方式步骤 (billing)
    """
    try:
        logger.info("处理支付方式步骤...")

        # 获取当前页面的STK
        current_stk = get_current_stk_from_page(driver) or stk

        # 构建billing API请求URL
        base_url = driver.current_url.split('/checkout')[0] if '/checkout' in driver.current_url else driver.current_url.split('/shop')[0] + '/shop'
        billing_url = f"{base_url}/checkoutx/billing"

        headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": base_url,
            "Referer": driver.current_url,
            "User-Agent": user_agent,
            "X-Requested-With": "Fetch",
            "modelVersion": "v2",
            "syntax": "graviton",
            "x-aos-model-page": "checkoutPage",
            "x-aos-stk": current_stk,
            "x-aos-ui-fetch-call-1": generate_aos_fetch_call_token()
        }

        # 构建POST数据 - 使用默认支付选项
        billing_data = {
            "checkout.billing.billingOptions.selectBillingOption": "INSTALLMENT"  # 或其他支付方式
        }

        # 添加查询参数
        params = {
            "_a": "continueFromBillingToReview",
            "_m": "checkout.billing"
        }

        logger.info(f"发送支付方式API请求到: {billing_url}")
        response = session.post(
            billing_url,
            headers=headers,
            data=billing_data,
            params=params,
            timeout=30
        )

        logger.info(f"支付方式API响应状态码: {response.status_code}")

        if response.status_code == 200:
            # 等待页面更新
            logger.info("等待页面更新...")
            time.sleep(5)  # 支付步骤可能需要更长时间

            # 检查页面是否已跳转到下一步
            try:
                current_url = driver.current_url
                page_title = driver.title
                logger.info(f"支付API调用后页面URL: {current_url}")
                logger.info(f"支付API调用后页面标题: {page_title}")

                # 保存最终状态用于调试
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                final_screenshot = f"after_billing_api_{timestamp}.png"
                final_html = f"after_billing_api_{timestamp}.html"

                try:
                    driver.save_screenshot(final_screenshot)
                    with open(final_html, "w", encoding="utf-8") as f:
                        f.write(driver.page_source)
                    logger.info(f"✅ 已保存支付API后状态: {final_screenshot}, {final_html}")
                except Exception as e:
                    logger.warning(f"保存支付API后状态失败: {e}")

                if "Review" in current_url or "review" in current_url.lower():
                    logger.info("✅ 支付方式成功，页面已跳转到订单确认步骤")
                    logger.info("🎉 结账流程已完成，请在浏览器中确认订单！")
                    return True
                elif "确认" in page_title or "review" in page_title.lower():
                    logger.info("✅ 支付方式成功，已到达订单确认页面")
                    logger.info("🎉 结账流程已完成，请在浏览器中确认订单！")
                    return True
                else:
                    logger.info("✅ 支付方式步骤完成")
                    logger.info("🎉 结账API流程已完成，请在浏览器中检查最终状态！")
                    return True
            except Exception as e:
                logger.warning(f"检查页面状态时出错: {e}")
                logger.info("✅ 支付方式API调用成功")
                logger.info("🎉 结账流程已完成，请在浏览器中确认订单！")
                return True
        else:
            logger.error(f"❌ 支付方式API请求失败: {response.status_code}")
            logger.error(f"响应内容: {response.text[:500]}")
            return False

    except Exception as e:
        logger.error(f"支付方式步骤出错: {e}")
        return False

def proceed_with_checkout_steps(driver, session, stk, user_agent):
    """
    执行结账的后续步骤：地址选择 → 配送方式 → 支付方式 → 订单确认
    基于HAR文件中的API请求序列
    """
    try:
        logger.info("🚀 开始执行结账后续步骤...")

        # 等待页面加载完成
        time.sleep(3)

        # 检查当前页面状态
        current_url = driver.current_url
        page_source = driver.page_source
        logger.info(f"当前页面URL: {current_url}")

        if "订单选项" in page_source and "安全结账" in page_source:
            logger.info("✅ 已成功进入结账页面，开始自动化步骤")
        else:
            logger.warning("⚠️ 页面状态不明确，尝试继续")

        # 步骤1: 处理地址选择 (fulfillment)
        logger.info("📍 步骤1: 处理地址选择...")
        fulfillment_success = handle_fulfillment_step(driver, session, stk, user_agent)

        if not fulfillment_success:
            logger.error("❌ 地址选择步骤失败")
            return False

        # 步骤2: 处理配送方式 (shipping)
        logger.info("🚚 步骤2: 处理配送方式...")
        shipping_success = handle_shipping_step(driver, session, stk, user_agent)

        if not shipping_success:
            logger.error("❌ 配送方式步骤失败")
            return False

        # 步骤3: 处理支付方式 (billing)
        logger.info("💳 步骤3: 处理支付方式...")
        billing_success = handle_billing_step(driver, session, stk, user_agent)

        if not billing_success:
            logger.error("❌ 支付方式步骤失败")
            return False

        logger.info("🎉 所有结账步骤完成！")

        # 验证最终状态
        final_verification = verify_checkout_completion(driver)
        if final_verification:
            logger.info("✅ 结账状态验证成功！")
        else:
            logger.warning("⚠️ 结账状态验证未通过，但API步骤已完成")

        return True

    except Exception as e:
        logger.error(f"结账步骤执行失败: {e}")
        return False

def verify_checkout_completion(driver):
    """
    验证结账是否真的完成
    检查页面状态、URL变化、关键元素等
    """
    try:
        logger.info("🔍 开始验证结账完成状态...")

        # 等待页面更新
        time.sleep(3)

        current_url = driver.current_url
        page_title = driver.title
        page_source = driver.page_source

        logger.info(f"最终页面URL: {current_url}")
        logger.info(f"最终页面标题: {page_title}")

        # 保存最终页面状态用于调试
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        final_screenshot = f"final_checkout_state_{timestamp}.png"
        final_html = f"final_checkout_state_{timestamp}.html"

        try:
            driver.save_screenshot(final_screenshot)
            with open(final_html, "w", encoding="utf-8") as f:
                f.write(page_source)
            logger.info(f"✅ 已保存最终状态文件: {final_screenshot}, {final_html}")
        except Exception as e:
            logger.warning(f"保存最终状态文件失败: {e}")

        # 检查成功指标
        success_indicators = []

        # 1. URL变化检查
        if "review" in current_url.lower():
            success_indicators.append("URL包含review（订单确认页面）")
        elif "billing" in current_url.lower():
            success_indicators.append("URL包含billing（支付页面）")
        elif "checkout" in current_url.lower():
            success_indicators.append("URL仍在checkout流程中")

        # 2. 页面标题检查
        if "确认" in page_title or "review" in page_title.lower():
            success_indicators.append("页面标题包含确认信息")
        elif "支付" in page_title or "billing" in page_title.lower():
            success_indicators.append("页面标题包含支付信息")

        # 3. 页面内容检查
        content_indicators = [
            ("订单确认", "order confirmation"),
            ("确认订单", "confirm order"),
            ("提交订单", "submit order"),
            ("立即购买", "buy now"),
            ("完成购买", "complete purchase"),
            ("支付方式", "payment method"),
            ("账单地址", "billing address"),
            ("订单摘要", "order summary"),
            ("总计", "total"),
            ("小计", "subtotal")
        ]

        found_content = []
        for chinese, english in content_indicators:
            if chinese in page_source or english in page_source.lower():
                found_content.append(chinese)

        if found_content:
            success_indicators.append(f"页面包含关键内容: {', '.join(found_content[:3])}")

        # 4. 检查是否有错误信息
        error_indicators = [
            "错误", "error", "失败", "failed",
            "无效", "invalid", "超时", "timeout"
        ]

        has_errors = any(error in page_source.lower() for error in error_indicators)
        if not has_errors:
            success_indicators.append("页面无明显错误信息")

        # 输出验证结果
        logger.info("📊 结账状态验证结果:")
        if success_indicators:
            for indicator in success_indicators:
                logger.info(f"  ✅ {indicator}")
        else:
            logger.warning("  ❌ 未找到明确的成功指标")

        if has_errors:
            logger.warning("  ⚠️ 页面可能包含错误信息")

        # 检查是否有提交按钮或下一步按钮
        button_selectors = [
            "button[type='submit']",
            "input[type='submit']",
            "button:contains('提交')",
            "button:contains('确认')",
            "button:contains('购买')",
            "button:contains('下一步')",
            ".button-primary",
            ".checkout-button",
            ".submit-button"
        ]

        found_buttons = []
        for selector in button_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    for element in elements[:2]:  # 只检查前2个
                        if element.is_displayed():
                            button_text = element.text.strip()
                            if button_text:
                                found_buttons.append(f"{selector}: '{button_text}'")
            except:
                continue

        if found_buttons:
            logger.info("🔘 发现的可操作按钮:")
            for button in found_buttons[:5]:  # 只显示前5个
                logger.info(f"  - {button}")

        # 综合判断
        is_successful = len(success_indicators) >= 2 and not has_errors

        if is_successful:
            logger.info("🎉 验证结果: 结账流程疑似成功完成！")
            if found_buttons:
                logger.info("💡 提示: 发现可操作按钮，可能需要手动确认最后步骤")
        else:
            logger.warning("⚠️ 验证结果: 结账状态不明确，请检查页面")

        return is_successful

    except Exception as e:
        logger.error(f"验证结账状态时出错: {e}")
        return False

def wait_for_manual_completion(driver, timeout_minutes=5):
    """
    等待用户手动完成最后的结账步骤
    监控页面变化，检测是否完成购买
    """
    try:
        logger.info(f"⏳ 等待用户手动完成结账（最多等待{timeout_minutes}分钟）...")
        logger.info("💡 请在浏览器中检查并完成最后的购买步骤")

        start_time = time.time()
        timeout_seconds = timeout_minutes * 60
        last_url = driver.current_url

        while time.time() - start_time < timeout_seconds:
            try:
                current_url = driver.current_url
                page_title = driver.title

                # 检查URL是否发生了重要变化
                if current_url != last_url:
                    logger.info(f"🔄 页面已跳转: {current_url}")
                    logger.info(f"📄 新页面标题: {page_title}")
                    last_url = current_url

                    # 检查是否到达了成功页面
                    success_patterns = [
                        "thank", "success", "complete", "confirmation",
                        "感谢", "成功", "完成", "确认", "订单号"
                    ]

                    if any(pattern in current_url.lower() or pattern in page_title.lower()
                           for pattern in success_patterns):
                        logger.info("🎉 检测到成功页面！")
                        return True

                # 检查页面内容变化
                page_source = driver.page_source
                if "订单已提交" in page_source or "购买成功" in page_source or "thank you" in page_source.lower():
                    logger.info("🎉 检测到购买成功信息！")
                    return True

                time.sleep(5)  # 每5秒检查一次

            except Exception as e:
                logger.warning(f"监控页面时出错: {e}")
                time.sleep(5)
                continue

        logger.info("⏰ 等待超时，停止监控")
        return False

    except Exception as e:
        logger.error(f"等待手动完成时出错: {e}")
        return False

def interactive_checkout_completion(driver):
    """
    交互式结账完成
    让用户选择是否需要手动完成最后步骤
    """
    try:
        logger.info("🤔 结账API步骤已完成，但可能需要手动确认最后步骤")

        # 检查当前页面状态
        current_url = driver.current_url
        page_title = driver.title

        logger.info(f"当前页面: {page_title}")
        logger.info(f"当前URL: {current_url}")

        # 尝试查找提交按钮
        submit_buttons = []
        button_selectors = [
            "button:contains('提交订单')",
            "button:contains('确认购买')",
            "button:contains('立即购买')",
            "button:contains('完成订单')",
            "input[value*='提交']",
            "input[value*='购买']",
            ".submit-button",
            ".checkout-button",
            ".buy-button"
        ]

        for selector in button_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        submit_buttons.append({
                            'element': element,
                            'text': element.text.strip(),
                            'selector': selector
                        })
            except:
                continue

        if submit_buttons:
            logger.info("🔘 发现可能的提交按钮:")
            for i, button in enumerate(submit_buttons[:3]):
                logger.info(f"  {i+1}. {button['text']} ({button['selector']})")

            # 询问用户是否要自动点击
            logger.info("💡 选项:")
            logger.info("  1. 让程序保持浏览器打开，您手动完成")
            logger.info("  2. 程序监控页面变化（等待5分钟）")
            logger.info("  3. 直接结束程序")

            # 这里可以添加用户输入逻辑，但为了不阻塞，我们选择监控模式
            logger.info("🔄 程序将监控页面变化，请在浏览器中手动完成购买...")

            # 监控页面变化
            manual_success = wait_for_manual_completion(driver, timeout_minutes=5)

            if manual_success:
                logger.info("🎉 检测到购买完成！")
                return True
            else:
                logger.info("⏰ 监控超时，请手动检查购买状态")
                return False
        else:
            logger.info("❓ 未发现明显的提交按钮，请手动检查页面")
            logger.info("💡 浏览器将保持打开状态，请手动完成购买")

            # 简单等待，让用户有时间操作
            logger.info("⏳ 程序将等待2分钟后结束...")
            time.sleep(120)
            return False

    except Exception as e:
        logger.error(f"交互式结账完成时出错: {e}")
        return False

def setup_browser_for_checkout():
    """
    为结账流程设置浏览器，使用持久化配置文件
    这样在需要登录时可以利用已保存的登录状态
    """
    global _global_driver

    try:
        # 如果已有浏览器实例，先关闭
        if _global_driver:
            try:
                _global_driver.quit()
            except:
                pass
            _global_driver = None

        # 设置Chrome选项，使用持久化配置文件
        options = uc.ChromeOptions()

        # 设置持久化配置文件
        profile_path = setup_chrome_profile_directory()
        if profile_path:
            options.add_argument(f'--user-data-dir={profile_path}')
            logger.info(f"✅ 结账浏览器将使用持久化配置文件: {profile_path}")

        # 结账时不使用headless模式，以便用户交互
        # options.add_argument('--headless')  # 注释掉headless

        # 其他优化参数
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36')

        # 启动浏览器
        _global_driver = uc.Chrome(options=options, use_subprocess=True)
        _global_driver.set_page_load_timeout(30)
        _global_driver.implicitly_wait(2)

        logger.info("✅ 结账浏览器启动成功，已配置持久化配置文件")
        return _global_driver

    except Exception as e:
        logger.error(f"设置结账浏览器失败: {e}")
        return None

def load_cookies_from_file(filename=COOKIES_FILE):
    """从文件加载cookies"""
    try:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            logger.info(f"✅ 已从 {filename} 加载 {len(cookies)} 个cookies")
            return cookies
        return None
    except Exception as e:
        logger.error(f"加载cookies失败: {e}")
        return None

def is_cookies_valid(cookies):
    """检查cookies是否仍然有效（包含关键认证cookies）"""
    if not cookies:
        logging.info("❌ cookies为空")
        return False
    
    # 检查关键认证cookies是否存在且未过期
    key_cookies = ['aasp', 'dqsid', 'acn01', 'as_sfa']  # 添加更多关键cookies
    cookie_names = [c.get('name') for c in cookies if c.get('name')]
    
    logging.info(f"🔍 检查cookies: {cookie_names}")
    
    # 检查是否包含关键cookies
    found_key_cookies = [name for name in key_cookies if name in cookie_names]
    logging.info(f"🔍 找到的关键cookies: {found_key_cookies}")
    
    # 只要有任何一个关键cookie就认为有效（降低门槛）
    has_key_cookies = len(found_key_cookies) > 0
    
    # 检查cookies是否过期（简单检查，实际应该检查expiry时间）
    current_time = time.time()
    valid_cookies = []
    expired_count = 0
    for cookie in cookies:
        if cookie.get('expiry'):
            if cookie['expiry'] > current_time:
                valid_cookies.append(cookie)
            else:
                expired_count += 1
        else:
            # 没有expiry的cookies通常是session cookies，认为是有效的
            valid_cookies.append(cookie)
    
    if expired_count > 0:
        logging.info(f"⚠️ 发现 {expired_count} 个过期cookies")
    
    logging.info(f"🔍 有效cookies数量: {len(valid_cookies)}")
    logging.info(f"🔍 是否包含关键cookies: {has_key_cookies}")
    
    return has_key_cookies and len(valid_cookies) > 0

# --- 核心函数 (来自 add_to_bag.py) ---

def get_atbtoken_stealth(product_url: str, use_saved_cookies=True, use_persistent_profile=True):
    """
    使用 undetected_chromedriver (伪装模式) 访问页面，获取动态安全令牌 atbtoken。
    优化版本：支持持久化Chrome配置文件，完全跳过2FA验证。
    """
    global _global_driver

    logging.info("启动伪装浏览器以获取动态 atbtoken...")

    # 如果已有浏览器实例，先关闭
    if _global_driver:
        try:
            _global_driver.quit()
        except:
            pass
        _global_driver = None

    options = uc.ChromeOptions()

    # 设置持久化Chrome配置文件
    profile_path = None
    has_existing_login = False
    if use_persistent_profile:
        profile_path = setup_chrome_profile_directory()
        if profile_path:
            options.add_argument(f'--user-data-dir={profile_path}')
            has_existing_login = check_profile_login_state(profile_path)
            if has_existing_login:
                logging.info("✅ 检测到已有登录状态，将尝试跳过2FA验证")
            else:
                logging.info("❌ 未检测到登录状态，首次运行需要完成2FA验证")

    # 优化启动参数，减少启动时间
    # 获取atbtoken阶段不需要登录，可以使用headless模式
    if HEADLESS:
        options.add_argument('--headless')
        logging.info("使用headless模式获取atbtoken（无需登录）")

    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    # 更激进的性能优化
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-plugins')
    options.add_argument('--disable-web-security')
    options.add_argument('--allow-running-insecure-content')
    # 减少内存使用
    options.add_argument('--memory-pressure-off')
    options.add_argument('--max_old_space_size=4096')
    # 禁用不必要的功能
    options.add_argument('--disable-background-timer-throttling')
    options.add_argument('--disable-backgrounding-occluded-windows')
    options.add_argument('--disable-renderer-backgrounding')
    # SSL和网络相关优化
    options.add_argument('--ignore-ssl-errors')
    options.add_argument('--ignore-certificate-errors')
    options.add_argument('--ignore-certificate-errors-spki-list')
    options.add_argument('--ignore-ssl-errors-spki-list')
    options.add_argument('--disable-features=VizDisplayCompositor')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-setuid-sandbox')
    # 设置真实的用户代理
    options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36')

    try:
        # 设置页面加载策略为 eager
        options.page_load_strategy = 'eager'
        # 使用 undetected_chromedriver 初始化 WebDriver，增加重试机制
        max_retries = 3
        for retry in range(max_retries):
            try:
                _global_driver = uc.Chrome(options=options, use_subprocess=True)
                _global_driver.set_page_load_timeout(30)  # 减少超时时间
                _global_driver.implicitly_wait(1)  # 减少隐式等待时间
                logging.info(f"✅ 浏览器启动成功 (第{retry+1}次尝试)")
                break
            except Exception as e:
                logging.warning(f"undetected_chromedriver 启动失败 (第{retry+1}次): {e}")
                if retry < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    # 尝试使用普通的 selenium webdriver 作为备用
                    logging.info("尝试使用普通 selenium webdriver 作为备用...")
                    try:
                        from selenium import webdriver
                        _global_driver = webdriver.Chrome(options=options)
                        _global_driver.set_page_load_timeout(30)
                        _global_driver.implicitly_wait(1)
                        logging.info("✅ 备用浏览器启动成功")
                        break
                    except Exception as backup_e:
                        logging.error(f"备用浏览器也启动失败: {backup_e}")
                        raise e

        # --- 检查持久化配置文件的登录状态 ---
        if use_persistent_profile and has_existing_login:
            logging.info("检查持久化配置文件的登录状态...")
            # 直接访问Apple购物袋页面检查登录状态
            _global_driver.get("https://www.apple.com.cn/shop/bag")
            time.sleep(2)  # 给页面更多时间加载

            # 检查是否已登录
            page_source = _global_driver.page_source
            current_url = _global_driver.current_url

            # 更全面的登录状态检查
            login_indicators = [
                'class="globalnav-link-sign-in"' in page_source,
                'id="ac-gn-signin"' in page_source,
                "signin" in current_url.lower(),
                "login" in current_url.lower(),
                "signIn" in current_url.lower()
            ]

            # 检查已登录状态的指标
            logged_in_indicators = [
                'id="ac-gn-signout"' in page_source,  # 登出按钮
                'class="globalnav-link-bag"' in page_source,  # 购物袋
                'data-analytics-element-engagement="bag"' in page_source,
                "购物袋" in page_source,
                "/shop/bag" in current_url,
                "apple.com.cn/shop/bag" in current_url
            ]

            is_logged_in = any(logged_in_indicators) and not any(login_indicators)

            logging.info(f"🔍 登录状态检查: URL={current_url}, 已登录={is_logged_in}")

            if is_logged_in:
                logging.info("✅ 持久化配置文件登录状态有效，成功跳过2FA验证！")
                # 直接获取atb cookie
                atb_cookie = _global_driver.get_cookie("as_atb")
                if atb_cookie:
                    all_cookies = _global_driver.get_cookies()
                    cookie_dict = {cookie['name']: cookie['value'] for cookie in all_cookies}
                    logging.info("✅ 使用持久化配置文件成功获取atb_cookie")
                    return {
                        'atb_cookie': atb_cookie.get('value'),
                        'all_cookies': cookie_dict,
                        'user_agent': _global_driver.execute_script("return navigator.userAgent;"),
                        'profile_restored': True
                    }
                else:
                    logging.warning("⚠️ 虽然看起来已登录，但无法获取as_atb cookie，继续正常流程")
            else:
                logging.info("⚠️ 持久化配置文件登录状态已过期，需要重新登录")

        # --- 备用：尝试恢复保存的cookies（仅在没有持久化配置文件时使用）---
        elif use_saved_cookies and not use_persistent_profile:
            saved_cookies = load_cookies_from_file()
            if saved_cookies and is_cookies_valid(saved_cookies):
                logging.info("尝试恢复保存的cookies...")
                # 先访问主域以设置cookies
                _global_driver.get("https://www.apple.com.cn/")
                time.sleep(0.5)

                for cookie in saved_cookies:
                    try:
                        # 清理cookie对象，确保格式正确
                        clean_cookie = {
                            'name': cookie.get('name'),
                            'value': cookie.get('value'),
                            'domain': cookie.get('domain', '.apple.com.cn'),
                            'path': cookie.get('path', '/')
                        }
                        _global_driver.add_cookie(clean_cookie)
                    except Exception as e:
                        logging.warning(f"添加cookie失败: {cookie.get('name')} - {e}")

                logging.info(f"已恢复 {len(saved_cookies)} 个cookies")

                # 测试cookies是否有效
                _global_driver.get("https://www.apple.com.cn/shop/bag")
                time.sleep(1)

                # 检查是否已登录
                page_source = _global_driver.page_source
                current_url = _global_driver.current_url

                login_indicators = [
                    'class="globalnav-link-sign-in"' in page_source,
                    'id="ac-gn-signin"' in page_source,
                    "signin" in current_url.lower(),
                    "login" in current_url.lower(),
                    "signIn" in current_url.lower()
                ]

                logged_in_indicators = [
                    'id="ac-gn-signout"' in page_source,
                    'class="globalnav-link-bag"' in page_source,
                    'data-analytics-element-engagement="bag"' in page_source,
                    "购物袋" in page_source,
                    "/shop/bag" in current_url
                ]

                is_logged_out = any(login_indicators) and not any(logged_in_indicators)

                if not is_logged_out:
                    logging.info("✅ 成功使用保存的cookies，无需重新登录")
                    atb_cookie = _global_driver.get_cookie("as_atb")
                    if atb_cookie:
                        all_cookies = _global_driver.get_cookies()
                        cookie_dict = {cookie['name']: cookie['value'] for cookie in all_cookies}
                        logging.info("✅ 使用保存的cookies成功获取atb_cookie")
                        return {
                            'atb_cookie': atb_cookie.get('value'),
                            'all_cookies': cookie_dict,
                            'user_agent': _global_driver.execute_script("return navigator.userAgent;"),
                            'cookies_restored': True
                        }
                    else:
                        logging.warning("⚠️ 虽然看起来已登录，但无法获取as_atb cookie")
                else:
                    logging.info("⚠️ 保存的cookies已过期或无效，需要重新登录")
            else:
                logging.info("未找到有效的保存cookies，将进行正常登录流程")

        # --- 注意：这里不进行登录，只是设置好配置文件以备后用 ---
        # 获取atbtoken不需要登录，登录将在结账时进行

        # --- 优化的Cookie获取逻辑 ---
        logging.info("直接访问产品页以获取 as_atb cookie (eager模式)...")

        # 增加页面访问的重试机制
        page_load_retries = 3
        for page_retry in range(page_load_retries):
            try:
                _global_driver.get(product_url)
                logging.info(f"✅ 产品页面加载成功 (第{page_retry+1}次尝试)")
                break
            except Exception as e:
                logging.warning(f"产品页面加载失败 (第{page_retry+1}次): {e}")
                if page_retry < page_load_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    raise e
        
        # 更激进的Cookie轮询策略
        logging.info("开始高频轮询检查 as_atb cookie...")
        atb_cookie = None
        max_retries = 15  # 减少轮询次数，但更频繁
        for i in range(max_retries):
            try:
                atb_cookie = _global_driver.get_cookie("as_atb")
                if atb_cookie:
                    logging.info(f"✅ 在产品页的第 {(i + 1) * 0.3:.1f} 秒成功捕获 as_atb cookie!")
                    break
                time.sleep(0.3)  # 减少间隔到0.3秒
            except Exception as e:
                logging.warning(f"获取cookie时出错 (第{i+1}次): {e}")
                time.sleep(0.5)

        if not atb_cookie:
            logging.error("在所有阶段都未能捕获 as_atb cookie，任务失败。")
            raise TimeoutException("无法获取as_atb cookie")

        logging.info(f"成功捕获 as_atb cookie: {atb_cookie.get('value')}")
        
        # 获取所有必要的cookies用于后端请求
        all_cookies = _global_driver.get_cookies()
        cookie_dict = {cookie['name']: cookie['value'] for cookie in all_cookies}
        
        logging.info(f"获取到的所有cookies: {list(cookie_dict.keys())}")
        
        # 返回一个包含cookie信息的token对象，供后端使用
        token_result = {
            'atb_cookie': atb_cookie.get('value'),
            'all_cookies': cookie_dict,
            'user_agent': _global_driver.execute_script("return navigator.userAgent;")
        }
        
        # 如果使用持久化配置文件，不需要额外保存cookies（已自动保存在配置文件中）
        if use_persistent_profile:
            logging.info("✅ 使用持久化配置文件，登录状态已自动保存")
        else:
            # 如果不是从保存的cookies恢复的会话，则保存当前cookies
            try:
                if save_cookies_to_file(all_cookies):
                    logging.info("✅ 已保存当前会话cookies，下次可跳过登录")
            except Exception as e:
                logging.warning(f"⚠️ 保存cookies失败: {e}")

        return token_result

    except Exception as e:
        logging.error(f"获取 atbtoken 时发生未知错误: {e}")
        if _global_driver:
            try:
                with open("final_debug.html", "w", encoding="utf-8") as f:
                    f.write(_global_driver.page_source)
                logging.info("已将当前浏览器页面保存到 final_debug.html 以供分析。")
            except Exception as se:
                logging.error(f"保存调试文件失败: {se}")
        return None

def add_to_cart_backend(sku_info: dict, token_info: dict, expected_item_count: int):
    """
    使用后台请求添加商品到购物车，并通过API验证结果。
    优化版本：减少网络延迟，优化请求策略。
    """
    post_url = sku_info['url']
    full_atb_cookie = token_info.get('atb_cookie')
    
    if not full_atb_cookie or len(full_atb_cookie.split('|')) < 3:
        logging.error("❌ as_atb cookie 格式不正确，无法提取 atbtoken。")
        return False

    atb_token_hash = full_atb_cookie.split('|')[2]
    # 每次添加时只打印一次hash
    if expected_item_count == 1:
        logging.info(f"提取的 atbtoken (hash): {atb_token_hash}")

    data = {
        'product': sku_info['sku'] + '/A',
        'purchaseOption': 'fullPrice',
        'step': 'select',
        'acpart': 'none',
        'igt': 'true',
        'add-to-cart': 'add-to-cart',
        'atbtoken': atb_token_hash,
        'quantity': 1, # 每次只添加1个，通过重复操作来增加数量
    }
    
    headers = {
        'User-Agent': token_info.get('user_agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36'),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Referer': sku_info['url'],
        'Origin': 'https://www.apple.com.cn',
        'Content-Type': 'application/x-www-form-urlencoded',
        # 添加连接优化头部
        'Connection': 'keep-alive',
        'Keep-Alive': 'timeout=5, max=1000'
    }
    
    try:
        # 使用连接池和超时优化，增加SSL错误处理
        session = requests.Session()
        
        # 配置SSL和重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("https://", adapter)
        session.mount("http://", adapter)
        
        # 设置更激进的超时
        timeout_config = (10, 20)  # (连接超时, 读取超时)
        
        for cookie_name, cookie_value in token_info.get('all_cookies', {}).items():
            session.cookies.set(cookie_name, cookie_value)

        logging.info(f"步骤1/2: 发送POST请求到 {post_url} (目标: {expected_item_count}件)")
        
        # 增加重试机制处理SSL错误
        max_retries = 3
        for retry in range(max_retries):
            try:
                add_response = session.post(post_url, data=data, headers=headers, 
                                          allow_redirects=True, timeout=timeout_config)
                break
            except requests.exceptions.SSLError as e:
                logging.warning(f"SSL错误 (第{retry+1}次): {e}")
                if retry < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    raise e
            except requests.exceptions.ConnectionError as e:
                logging.warning(f"连接错误 (第{retry+1}次): {e}")
                if retry < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    raise e

        if not (add_response.status_code == 200 and "step=attach" in add_response.url):
            logging.warning("⚠️ 添加商品到购物车的POST请求似乎未成功。")
            logging.warning(f"最终URL: {add_response.url}")
            return False

        logging.info("✅ POST请求成功，已跳转到attach页面。")
        logging.info("步骤2/2: 发送GET请求到状态API以验证购物车内容...")
        
        status_url = "https://www.apple.com.cn/shop/bag/status?apikey=SJHJUH4YFCTTPD4F4"
        
        # 同样为状态API添加重试机制
        for retry in range(max_retries):
            try:
                status_response = session.get(status_url, headers=headers, timeout=timeout_config)
                break
            except requests.exceptions.SSLError as e:
                logging.warning(f"状态API SSL错误 (第{retry+1}次): {e}")
                if retry < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    raise e
            except requests.exceptions.ConnectionError as e:
                logging.warning(f"状态API连接错误 (第{retry+1}次): {e}")
                if retry < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    raise e

        if status_response.status_code != 200:
            logging.error(f"❌ 无法获取购物车状态。状态码: {status_response.status_code}")
            return False

        cart_data = status_response.json()
        logging.info(f"购物车状态API响应: {cart_data}")

        item_count = cart_data.get('items', 0)
        if item_count == expected_item_count:
            logging.info(f"🎉 验证成功！API确认购物车中现在有 {item_count} 件商品。")
            return True
        else:
            logging.warning(f"⚠️ 验证失败。API返回购物车中有 {item_count} 件商品，预期为 {expected_item_count} 件。")
            return False

    except Exception as e:
        logging.error(f"后端操作或验证过程中发生错误: {e}")
        return False

def get_checkout_data_in_browser(driver):
    """
    在浏览器环境中获取 STK 和 商品ID。
    这是结账流程的关键步骤。
    """
    logger.info("在浏览器环境中获取 STK 和 商品ID...")
    stk, item_ids = None, []
    
    try:
        # 检查当前是否已经在结账页面
        current_url = driver.current_url
        if "checkout" in current_url:
            logger.info("当前已在结账页面，直接解析STK...")
            page_source = driver.page_source
            with open("checkout_page_debug.html", "w", encoding="utf-8") as f:
                f.write(page_source)
            logger.info("已保存结账页面源码到 checkout_page_debug.html")
        else:
            logger.info("导航到购物车页面以解析STK...")
            driver.get("https://www.apple.com.cn/shop/bag")
            time.sleep(0.3)
            
            # 保存页面源码用于调试
            page_source = driver.page_source
            with open("cart_page_debug.html", "w", encoding="utf-8") as f:
                f.write(page_source)
            logger.info("已保存购物车页面源码到 cart_page_debug.html")
        
        # 方法1: 从init_data脚本节点解析 STK（更可靠）
        logger.info("尝试从 script#init_data 中解析STK...")
        try:
            script_json = driver.execute_script("var e=document.getElementById('init_data'); return e? e.textContent : null;")
            if script_json:
                try:
                    parsed = json.loads(script_json)
                    meta = parsed.get('meta', {})
                    h = meta.get('h', {})
                    cand = h.get('x-aos-stk')
                    if cand:
                        stk = cand
                        logger.info(f"✅ 从 script#init_data 解析到STK: {stk}")
                    else:
                        logger.warning("script#init_data 中未找到 x-aos-stk")
                except Exception as je:
                    logger.warning(f"解析 script#init_data JSON 失败: {je}")
            else:
                logger.warning("未找到 script#init_data 或其内容为空")
        except Exception as e:
            logger.warning(f"读取 script#init_data 失败: {e}")

        # 方法1b: 若仍未获取到，再尝试 window.init_data
        if not stk:
            logger.info("尝试从 window.init_data 获取STK...")
            try:
                init_data = driver.execute_script("return window.init_data;")
                if isinstance(init_data, dict):
                    meta = init_data.get('meta', {})
                    h = meta.get('h', {})
                    cand = h.get('x-aos-stk')
                    if cand:
                        stk = cand
                        logger.info(f"✅ 从 window.init_data 解析到STK: {stk}")
                else:
                    logger.warning("window.init_data 为空或格式不正确")
            except Exception as e:
                logger.warning(f"获取 window.init_data 失败: {e}")
        
        # 方法2: 直接从整页源码提取 UUID 形式的 item id（更快更稳）
        import re
        uuids = re.findall(r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}", page_source, flags=re.IGNORECASE)
        if uuids:
            seen = set()
            item_ids = [u for u in uuids if not (u in seen or seen.add(u))]
            logger.info(f"✅ 从页面源码中提取到UUID形式的商品ID: {item_ids}")
        else:
            # 回退到在script标签中定位 cart-items-item-* 的方式
            scripts = driver.find_elements(By.TAG_NAME, "script")
            for script in scripts:
                script_content = script.get_attribute("innerHTML")
                if not script_content:
                    continue
                if ("cart-items-item" in script_content) or ("partNumber" in script_content):
                    id_matches = re.findall(r'"id"\s*:\s*"cart-items-item-([^"]+)"', script_content)
                    if id_matches:
                        item_ids = id_matches
                        logger.info(f"✅ 从script中提取到商品ID: {item_ids}")
                        break
                    part_matches = re.findall(r'"partNumber"\s*:\s*"([^"]+)"', script_content)
                    if part_matches:
                        logger.info(f"找到商品型号: {part_matches}")
        
        # 方法5: 从页面URL或meta标签中查找STK
        if not stk:
            logger.info("尝试从页面URL或meta标签中查找STK...")
            try:
                current_url = driver.current_url
                if "stk=" in current_url:
                    stk = current_url.split("stk=")[1].split("&")[0]
                    logger.info(f"✅ 从URL中提取到STK: {stk}")
                
                # 查找meta标签
                meta_tags = driver.find_elements(By.TAG_NAME, "meta")
                for meta in meta_tags:
                    if meta.get_attribute("name") == "stk":
                        stk = meta.get_attribute("content")
                        logger.info(f"✅ 从meta标签中提取到STK: {stk}")
                        break
            except Exception as e:
                logger.warning(f"从页面URL或meta标签查找STK失败: {e}")
        
        # 最终检查
        if not stk: 
            logger.error("❌ 最终未能获取到STK token")
        if not item_ids:
            logger.error("❌ 最终未能获取到商品ID")
        
        # 如果仍然没有获取到，尝试从页面源码中查找
        if not stk or not item_ids:
            logger.info("尝试从页面源码中查找STK和商品ID...")
            try:
                # 查找包含商品信息的script标签
                scripts = driver.find_elements(By.TAG_NAME, "script")
                for script in scripts:
                    script_content = script.get_attribute("innerHTML")
                    if script_content and ("MYTR3CH" in script_content or "stk" in script_content):
                        logger.info("找到包含商品信息的script标签")
                        # 这里可以添加更多的解析逻辑
                        break
            except Exception as e:
                logger.warning(f"从页面源码查找失败: {e}")

    except Exception as e:
        logger.error(f"在浏览器中获取购物车数据时出错: {e}")
        # 保存错误截图
        try:
            screenshot_path = f"get_checkout_data_error_{int(time.time())}.png"
            driver.save_screenshot(screenshot_path)
            logger.info(f"已保存错误截图到: {screenshot_path}")
        except Exception as se:
            logger.error(f"保存截图失败: {se}")
        
        # 保存页面源码
        try:
            with open(f"error_page_source_{int(time.time())}.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            logger.info("已保存错误页面源码")
        except Exception as se:
            logger.error(f"保存页面源码失败: {se}")

    return stk, item_ids

# --- 结账相关函数 (保留原有逻辑) ---

def checkout_via_browser(driver):
    try:
        logger.info("开始通过浏览器点击结账按钮...")
        # We should already be on the bag page.
        # Find and click the checkout button.
        checkout_button = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable((By.ID, "shoppingCart.actions.navCheckout"))
        )
        driver.execute_script("arguments[0].click();", checkout_button)
        logger.info("✅ 已点击结账按钮，等待页面跳转...")

        # Wait for the next page, which should be the shipping/fulfillment page.
        WebDriverWait(driver, 30).until(EC.url_contains("checkout"))
        logger.info(f"✅ 成功进入结账流程页面: {driver.current_url}")
        return True
    except Exception as e:
        logger.error(f"❌ 通过浏览器点击结账失败: {e}")
        driver.save_screenshot("error_browser_checkout.png")
        return False

def to_base36(value):
    if not isinstance(value, int): 
        raise TypeError("Value must be an integer.")
    if value == 0: 
        return "0"
    charset = "0123456789abcdefghijklmnopqrstuvwxyz"
    base36_str, sign = "", ""
    if value < 0: 
        sign, value = "-", -value
    while value > 0: 
        value, remainder = divmod(value, 36)
        base36_str = charset[remainder] + base36_str
    return sign + base36_str

def generate_aos_fetch_call_token():
    random_part = ''.join(random.choice(string.ascii_lowercase + string.digits) for _ in range(10))
    timestamp_part = to_base36(int(time.time() * 1000))
    return f"{random_part}-{timestamp_part}"

def generate_checkout_data(item_ids):
    data = {
        "shoppingCart.recommendations.recommendedItem.part": "", 
        "shoppingCart.locationConsent.locationConsent": "false",
        "shoppingCart.summary.promoCode.promoCode": "", 
        "shoppingCart.actions.fcscounter": "", 
        "shoppingCart.actions.fcsdata": ""
    }
    default_state = "广东"
    default_province_city_district = "广东梅州丰顺县"
    
    for item_id in item_ids:
        data.update({
            f"shoppingCart.items.item-{item_id}.isIntentToGift": "false", 
            f"shoppingCart.items.item-{item_id}.itemQuantity.quantity": "1",
            f"shoppingCart.items.item-{item_id}.delivery.lineDeliveryOptions.address.provinceCityDistrictTabs.state": default_state,
            f"shoppingCart.items.item-{item_id}.delivery.lineDeliveryOptions.address.provinceCityDistrictTabs.provinceCityDistrict": default_province_city_district,
            f"shoppingCart.items.item-{item_id}.delivery.lineDeliveryOptions.address.provinceCityDistrictTabs.countryCode": "CN",
        })
    return data

def checkout_improved(session, stk, item_ids, user_agent, login_retry: int = 0):
    """
    执行结账流程
    """
    if not stk or not item_ids: 
        return False
    
    url = "https://www.apple.com.cn/shop/bagx/checkout_now?_a=checkout&_m=shoppingCart.actions"
    headers = {
        "accept": "*/*", 
        "content-type": "application/x-www-form-urlencoded", 
        "modelversion": "v2", 
        "origin": "https://www.apple.com.cn",
        "referer": "https://www.apple.com.cn/shop/bag", 
        "syntax": "graviton",
        "user-agent": user_agent,
        "x-aos-model-page": "cart", 
        "x-aos-stk": stk, 
        "x-aos-ui-fetch-call-1": generate_aos_fetch_call_token(), 
        "x-requested-with": "Fetch"
    }
    data = generate_checkout_data(item_ids)
    
    logger.info(f"准备为 {len(item_ids)} 个商品进行结账...")
    try:
        # 为结账请求添加重试机制
        max_retries = 3
        for retry in range(max_retries):
            try:
                response = session.post(url, headers=headers, data=data, timeout=(15, 30))
                response.raise_for_status()
                logger.info(f"结账请求状态码: {response.status_code}")
                response_json = response.json()
                break
            except requests.exceptions.SSLError as e:
                logger.warning(f"结账SSL错误 (第{retry+1}次): {e}")
                if retry < max_retries - 1:
                    time.sleep(3)
                    continue
                else:
                    raise e
            except requests.exceptions.ConnectionError as e:
                logger.warning(f"结账连接错误 (第{retry+1}次): {e}")
                if retry < max_retries - 1:
                    time.sleep(3)
                    continue
                else:
                    raise e
        
        if response_json.get("head", {}).get("status") == 200 and response_json.get("body", {}).get("redirectURL"):
            logger.info(f"🎉 结账成功！服务器要求跳转到: {response_json['body']['redirectURL']}")
            return True
        elif response_json.get("head", {}).get("status") == 302:
            # 先尝试 API 首因子登录（若提供了账号密码）
            login_url = response_json.get("head", {}).get("data", {}).get("url") or ""
            # 超过上限则停止，避免无限循环
            if login_retry >= MAX_302_RETRIES:
                logger.error(f"检测到302且已达到最大重试次数({MAX_302_RETRIES})，停止本次结账。")
                return False
            if APPLE_ID and APPLE_PWD:
                log_func = lambda msg: logger.info(msg)
                account_dict = {"email": APPLE_ID, "password": APPLE_PWD}

                # 重新启动浏览器，使用持久化配置文件
                log_func("🔄 重新启动浏览器以使用持久化配置文件...")
                checkout_driver = setup_browser_for_checkout()

                if checkout_driver:
                    # 更新全局浏览器实例
                    _global_driver = checkout_driver

                    # 导航到登录页面
                    log_func(f"导航到登录页: {login_url}")
                    checkout_driver.get(login_url)
                    time.sleep(2)

                    # 检查是否已经在结账页面（说明已经登录）
                    current_url = checkout_driver.current_url
                    page_source = checkout_driver.page_source
                    page_title = checkout_driver.title

                    log_func(f"页面标题: {page_title}")

                    # 更精确地检查是否已经在结账页面
                    is_checkout_page = (
                        ("checkout" in current_url and "signin" not in current_url.lower()) and
                        "订单选项" in page_source and
                        "signin" not in current_url.lower() and
                        "signIn" not in current_url
                    )

                    # 检查是否在登录页面
                    is_login_page = (
                        "signin" in current_url.lower() or
                        "signIn" in current_url or
                        "登录" in page_title or
                        "使用你的 Apple 账户结账" in page_source or
                        "Apple ID" in page_source
                    )

                    if is_checkout_page:
                        log_func("✅ 检测到已在结账页面，说明登录状态有效，直接执行后续步骤")
                        browser_ok = True
                    elif is_login_page:
                        log_func("🔐 检测到登录页面，需要进行登录")
                        browser_ok = auto_login_with_persistent_profile(checkout_driver, account_dict, log_func, login_url)
                    else:
                        log_func("❓ 页面状态不明确，尝试登录")
                        browser_ok = auto_login_with_persistent_profile(checkout_driver, account_dict, log_func, login_url)
                else:
                    log_func("❌ 无法启动结账浏览器")
                    browser_ok = False

                if browser_ok:
                    logger.info("浏览器登录成功，准备重试结账...")

                    # Saving debug files as requested
                    ts = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                    debug_screenshot = f"post_login_debug_{ts}.png"
                    debug_html = f"post_login_debug_{ts}.html"
                    try:
                        logger.info(f"当前页面URL: {_global_driver.current_url}")
                        logger.info(f"当前页面标题: {_global_driver.title}")
                        _global_driver.save_screenshot(debug_screenshot)
                        with open(debug_html, "w", encoding="utf-8") as f:
                            f.write(_global_driver.page_source)
                        logger.info(f"已保存登录后的调试文件: {debug_screenshot}, {debug_html}")
                    except Exception as e:
                        logger.error(f"保存登录后调试文件失败: {e}")
                    
                    # 1. Refresh the checkout_session with the NEW cookies from the browser
                    logger.info("正在从主浏览器同步最新的会话状态...")
                    session.cookies.clear() # Clear the old cookies
                    all_browser_cookies = _global_driver.get_cookies()
                    for cookie in all_browser_cookies:
                        try:
                            session.cookies.set(cookie['name'], cookie['value'], domain=cookie.get('domain'))
                        except Exception:
                            pass
                    logger.info(f"会话同步完成，当前共有 {len(all_browser_cookies)} 个 cookies。")

                    # 2. Get the new STK and item_ids using the main browser
                    logger.info("正在从主浏览器获取最新的STK和商品ID...")
                    new_stk, new_item_ids = get_checkout_data_in_browser(_global_driver)
                    
                    # 如果当前已在结账页面，需要获取结账页面的STK而不是购物车页面的STK
                    current_url = _global_driver.current_url
                    if "checkout" in current_url:
                        logger.info("检测到已在结账页面，获取结账页面的STK...")
                        # 从当前结账页面获取STK
                        try:
                            script_json = _global_driver.execute_script("var e=document.getElementById('init_data'); return e? e.textContent : null;")
                            if script_json:
                                parsed = json.loads(script_json)
                                meta = parsed.get('meta', {})
                                h = meta.get('h', {})
                                checkout_stk = h.get('x-aos-stk')
                                if checkout_stk:
                                    stk = checkout_stk
                                    logger.info(f"✅ 从结账页面获取到新的STK: {stk}")
                                else:
                                    logger.warning("结账页面中未找到STK")
                        except Exception as e:
                            logger.warning(f"从结账页面获取STK失败: {e}")
                    else:
                        if new_stk:
                            stk = new_stk
                            logger.info(f"已刷新到登录后的 STK: {stk}")
                        else:
                            logger.warning("登录后未能获取到新的STK，可能会导致失败。")
                    
                    if new_item_ids:
                        item_ids = new_item_ids
                        logger.info(f"已刷新到登录后的 item_ids: {item_ids}")
                    else:
                        logger.warning("登录后未能获取到新的item_ids，可能会导致失败。")

                    # 3. 检查是否已在结账页面，如果是则直接执行后续步骤（跳过隐私弹窗处理）
                    if "checkout" in current_url and ("订单选项" in _global_driver.page_source or "安全结账" in _global_driver.page_source):
                        logger.info("✅ 已成功进入结账页面，跳过隐私弹窗处理，直接执行后续步骤...")

                        # 同步浏览器cookies到session
                        session.cookies.clear()
                        all_browser_cookies = _global_driver.get_cookies()
                        for cookie in all_browser_cookies:
                            try:
                                session.cookies.set(cookie['name'], cookie['value'], domain=cookie.get('domain'))
                            except Exception:
                                pass
                        logger.info(f"会话同步完成，当前共有 {len(all_browser_cookies)} 个 cookies。")

                        # 直接执行结账后续步骤，不需要处理隐私弹窗
                        checkout_steps_success = proceed_with_checkout_steps(_global_driver, session, stk, user_agent)

                        if checkout_steps_success:
                            logger.info("🎉 结账API步骤完成！")

                            # 进行交互式结账完成验证
                            logger.info("🔍 开始验证最终结账状态...")
                            final_success = interactive_checkout_completion(_global_driver)

                            if final_success:
                                logger.info("🎉 购买流程完全完成！")
                                return True
                            else:
                                logger.info("⚠️ API步骤完成，但最终状态需要手动确认")
                                logger.info("💡 浏览器将保持打开，请手动检查和完成购买")
                                # 不关闭浏览器，让用户手动操作
                                return True  # 仍然返回True，因为API步骤成功了
                        else:
                            logger.error("❌ 结账步骤执行失败")
                            return False
                    else:
                        logger.warning("未能确认结账页面状态，尝试传统重试方式")
                        # 如果不在结账页面，使用传统的重试方式
                        return checkout_improved(session, stk, item_ids, user_agent, login_retry=login_retry + 1)
                logger.warning("API 登录未成功")
            else:
                logger.warning("未提供APPLE_ID/APPLE_PWD环境变量，无法进行API登录")
            # 若禁止浏览器兜底，则直接返回失败，避免弹出窗口和页面刷新
            if PREFER_API_LOGIN or DISABLE_BROWSER_LOGIN_FALLBACK:
                logger.error("登录仍未完成且已禁用浏览器回退。请完成2FA或稍后重试。")
                return False
            # 否则使用可见浏览器兜底
            return handle_login_and_retry(response_json, _global_driver, session, user_agent, item_ids)
        else:
            logger.error(f"结账请求失败，服务器响应: {response.json()}")
            return False
    except Exception as e: 
        logger.error(f"结账请求过程中发生错误: {e}")
        return False

def handle_login_and_retry(response_json, driver, session, user_agent, item_ids):
	"""当checkout返回302登录时：
	- 若当前为无头，则新建一个可见浏览器
	- 先打开 https://www.apple.com.cn/ 以便注入 .apple.com.cn 域 cookies
	- 再到 https://www.apple.com.cn/shop/bag 作为 Referer
	- 通过 JS 将 location 切到 ssi 登录链接，避免非法参数
	- 登录完成后回灌 cookies 到 session，重新获取 STK 并重试一次结账
	"""
	try:
		login_url = response_json.get("head", {}).get("data", {}).get("url")
		if not login_url:
			logger.error("未找到登录跳转URL")
			return False

		# 若当前为无头，另启一个可见浏览器；否则沿用现有 driver
		login_driver = driver
		if HEADLESS:
			logger.info("当前为无头模式，将启动可见浏览器用于登录...")
			opts = uc.ChromeOptions()
			# 显式可见：不添加 --headless
			opts.add_argument('--disable-gpu')
			opts.add_argument('--no-sandbox')
			opts.add_argument('--disable-dev-shm-usage')
			opts.add_argument('--disable-blink-features=AutomationControlled')
			opts.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36')
			login_driver = uc.Chrome(options=opts, use_subprocess=True)

		# 1) 先 prime 主域，才能注入 .apple.com.cn cookies
		login_driver.get("https://www.apple.com.cn/")
		time.sleep(0.3)

		# 复制已有 cookies（来自原 driver）到可见浏览器
		try:
			orig_cookies = driver.get_cookies()
		except Exception:
			orig_cookies = []
		for c in orig_cookies:
			try:
				cookie_obj = {
					"name": c.get('name'),
					"value": c.get('value'),
					"path": c.get('path', '/'),
					"domain": c.get('domain') if (c.get('domain') and 'apple.com.cn' in c.get('domain')) else '.apple.com.cn',
				}
				login_driver.add_cookie(cookie_obj)
			except Exception:
				continue

		# 2) 进入购物袋页，作为后续 ssi 跳转的 Referer
		login_driver.get("https://www.apple.com.cn/shop/bag")
		time.sleep(0.5)

		# 3) 通过 JS 切到 ssi 登录链接（保留 Referer）
		logger.info(f"检测到需要登录，正在打开登录页面: {login_url}")
		login_driver.execute_script("document.location = arguments[0];", login_url)
		logger.info("请在打开的浏览器页面中完成登录，脚本将短暂轮询登录状态...")

		# 轮询登录状态（最多 ~90 秒）
		for _ in range(90):
			try:
				login_driver.get("https://www.apple.com.cn/shop/bag")
				time.sleep(1.0)
				init_data = login_driver.execute_script("return window.init_data;")
				if isinstance(init_data, dict):
					# 粗判是否出现账户相关入口，可视为已登录
					logger.info("检测到 window.init_data，可能已登录")
					break
			except Exception:
				pass
		else:
			logger.error("等待登录超时")
			return False

		# 回灌 cookies 到 requests.Session()
		all_browser_cookies = login_driver.get_cookies()
		for cookie in all_browser_cookies:
			try:
				session.cookies.set(cookie['name'], cookie['value'], domain=cookie.get('domain'))
			except Exception:
				continue
		logger.info(f"登录后已同步 {len(all_browser_cookies)} 个浏览器 cookies 到 session。")

		# 重新获取 STK（以防刷新）
		stk, _ = get_checkout_data_in_browser(login_driver)
		if not stk:
			logger.error("登录后未能重新获取STK，无法继续结账")
			return False

		# 重试一次结账
		return checkout_improved(session, stk, item_ids, user_agent)
	except Exception as e:
		logger.error(f"处理登录并重试时出错: {e}")
		return False

def _parse_boot_args_from_html(html_text: str):
	try:
		# 提取 <script type="application/json" class="boot_args">...</script>
		start_tag = 'class="boot_args"'
		i = html_text.find(start_tag)
		if i == -1:
			return None
		# 向前找到 <script
		s = html_text.rfind('<script', 0, i)
		if s == -1:
			return None
		# 向后找到 </script>
		e = html_text.find('</script>', i)
		if e == -1:
			return None
		json_text = html_text[html_text.find('>', s) + 1:e].strip()
		return _json_for_creds.loads(json_text)
	except Exception:
		return None


def _get_idmsa_headers(user_agent: str, scnt: str = None, sess_id: str = None):
	headers = {
		"User-Agent": user_agent,
		"Accept": "application/json, text/javascript, */*; q=0.01",
		"Referer": "https://idmsa.apple.com.cn/",
		"Origin": "https://idmsa.apple.com.cn",
		"X-Requested-With": "XMLHttpRequest",
		"X-Apple-Widget-Key": "a797929d224abb1cc663bb187bbcd02f7172ca3a84df470380522a7c6092118b",
		"X-Apple-Domain-Id": "45",
		"X-Apple-OAuth-Client-Id": "a797929d224abb1cc663bb187bbcd02f7172ca3a84df470380522a7c6092118b",
		"X-Apple-OAuth-Client-Type": "firstPartyAuth",
		"X-Apple-OAuth-Redirect-URI": "https://secure9.www.apple.com.cn",
		"X-Apple-OAuth-Response-Mode": "web_message",
		"X-Apple-OAuth-Response-Type": "code",
		"X-Apple-Locale": "zh_CN",
		"Content-Type": "application/json",
	}
	if scnt:
		headers["scnt"] = scnt
	if sess_id:
		headers["X-Apple-ID-Session-Id"] = sess_id
	# 追加从历史响应收集的动态头
	try:
		if _IDMSA_HEADER_HINTS.get("X-Apple-Auth-Attributes"):
			headers["X-Apple-Auth-Attributes"] = _IDMSA_HEADER_HINTS["X-Apple-Auth-Attributes"]
		if _IDMSA_HEADER_HINTS.get("X-APPLE-HC"):
			headers["X-APPLE-HC"] = _IDMSA_HEADER_HINTS["X-APPLE-HC"]
	except Exception:
		pass
	return headers


def _fetch_2fa_context(session: requests.Session, user_agent: str, scnt: str = None, sess_id: str = None):
	# 拉取2FA页面并解析 boot_args 获取受信电话号码ID
	resp = session.get("https://idmsa.apple.com.cn/appleauth/auth", headers=_get_idmsa_headers(user_agent, scnt, sess_id), timeout=15)
	scnt2 = resp.headers.get("scnt") or scnt
	phone_id = None
	
	try:
		# The response is now pure JSON, not HTML
		data = resp.json()
		
		# The structure is now flat
		trusted = data.get("trustedPhoneNumbers", [])
		if trusted:
			# We'll take the first phone number's ID
			phone_id = trusted[0].get("id")
			logger.info(f"成功从2FA上下文中解析到 phone_id: {phone_id}")
		else:
			logger.warning("在2FA上下文中未找到 trustedPhoneNumbers 或列表为空。")

	except Exception as e:
		logger.error(f"解析2FA上下文JSON失败: {e}")
		# Save the failed content for debugging
		try:
			debug_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "2fa_context_page_failed.html")
			with open(debug_path, "w", encoding="utf-8") as f:
				f.write(resp.text)
			logger.info(f"已将失败的2FA上下文页面保存到 {debug_path} 以供调试。")
		except Exception as se:
			logger.warning(f"保存失败的2FA上下文页面也失败了: {se}")

	return phone_id, scnt2


def _update_idmsa_hints_from_resp(resp):
	try:
		if not resp:
			return
		v = resp.headers.get("X-Apple-Auth-Attributes")
		if v:
			_IDMSA_HEADER_HINTS["X-Apple-Auth-Attributes"] = v
		v2 = resp.headers.get("X-APPLE-HC")
		if v2:
			_IDMSA_HEADER_HINTS["X-APPLE-HC"] = v2
	except Exception:
		pass


def _poll_two_sv_state(session: requests.Session, user_agent: str, scnt: str = None, sess_id: str = None, timeout_s: int = 60):
	"""轮询 IDMSA /auth 页面，直到 twoSV 验证状态为 succeeded 或超时。"""
	end_ts = time.time() + timeout_s
	last_scnt = scnt
	while time.time() < end_ts:
		try:
			resp = session.get("https://idmsa.apple.com.cn/appleauth/auth", headers=_get_idmsa_headers(user_agent, last_scnt, sess_id), timeout=15)
			last_scnt = resp.headers.get("scnt") or last_scnt
			_update_idmsa_hints_from_resp(resp)
			boot = _parse_boot_args_from_html(resp.text)
			if isinstance(boot, dict):
				state = None
				try:
					state = boot.get("direct", {}).get("twoSV", {}).get("phoneNumberVerification", {}).get("state")
				except Exception:
					state = None
				if state and str(state).lower() == "succeeded":
					return True, last_scnt
		except Exception:
			pass
		time.sleep(1.0)
	return False, last_scnt


def _send_2fa_code(session: requests.Session, user_agent: str, scnt: str = None, sess_id: str = None, phone_id: int = None, mode: str = "sms") -> bool:
	url = "https://idmsa.apple.com.cn/appleauth/auth/verify/phone"
	payload = {"phoneNumber": {"id": phone_id}, "mode": mode}
	headers = _get_idmsa_headers(user_agent, scnt, sess_id)
	try:
		resp = session.post(url, headers=headers, json=payload, timeout=20)
		return resp.status_code in (200, 204)
	except Exception:
		return False


def _submit_2fa_code(session: requests.Session, user_agent: str, scnt: str = None, sess_id: str = None, code: str = None):
	url = "https://idmsa.apple.com.cn/appleauth/auth/verify/securitycode"
	payload = {"securityCode": {"code": code}}
	headers = _get_idmsa_headers(user_agent, scnt, sess_id)
	try:
		resp = session.post(url, headers=headers, json=payload, timeout=20)
		ok = resp.status_code in (200, 204)
		_update_idmsa_hints_from_resp(resp)
		return (ok, resp.headers.get("scnt") or scnt)
	except Exception:
		return False, scnt


def _submit_trusteddevice_code(session: requests.Session, user_agent: str, scnt: str = None, sess_id: str = None, code: str = None):
	"""当未使用短信验证码时，部分账户走 trusteddevice 验证端点。"""
	url = "https://idmsa.apple.com.cn/appleauth/auth/verify/trusteddevice/securitycode"
	payload = {"securityCode": {"code": code}}
	headers = _get_idmsa_headers(user_agent, scnt, sess_id)
	try:
		resp = session.post(url, headers=headers, json=payload, timeout=20)
		ok = resp.status_code in (200, 204)
		_update_idmsa_hints_from_resp(resp)
		return (ok, resp.headers.get("scnt") or scnt)
	except Exception:
		return False, scnt


def _trust_browser(session: requests.Session, user_agent: str, scnt: str = None, sess_id: str = None):
	url = "https://idmsa.apple.com.cn/appleauth/auth/2sv/trust"
	headers = _get_idmsa_headers(user_agent, scnt, sess_id)
	try:
		resp = session.get(url, headers=headers, timeout=15)
		if resp.status_code not in (200, 204):
			resp = session.put(url, headers=headers, timeout=15)
		_update_idmsa_hints_from_resp(resp)
		return (resp.status_code in (200, 204), resp.headers.get("scnt") or scnt)
	except Exception:
		return False, scnt


def _read_2fa_code_from_env_or_file():
	code = os.environ.get('APPLE_2FA_CODE')
	if code:
		return code.strip()
	# 尝试 twofa_code.txt
	try:
		base_dir = os.path.dirname(os.path.abspath(__file__))
		p = os.path.join(base_dir, 'twofa_code.txt')
		if os.path.exists(p):
			with open(p, 'r', encoding='utf-8') as f:
				c = f.read().strip()
				if c:
					return c
	except Exception:
		pass
	return None


def _prompt_2fa_code_ui():
	"""弹出一个简单的GUI输入框让用户输入6位验证码。"""
	if not _TK_AVAILABLE or not USE_UI_2FA_PROMPT:
		return None
	try:
		root = _tk.Tk()
		root.withdraw()
		root.attributes('-topmost', True)
		while True:
			code = _simpledialog.askstring("两步验证", "请输入6位验证码：", parent=root)
			if code is None:
				return None
			code = code.strip()
			if len(code) == 6 and code.isdigit():
				return code
			_messagebox.showerror("格式错误", "请输入6位数字验证码")
	except Exception:
		return None
	finally:
		try:
			root.destroy()
		except Exception:
			pass


def _dump_response_for_debug(prefix: str, url: str, headers: dict, payload: dict, resp=None):
	if not DEBUG_IDMSA_DUMP:
		return
	try:
		ts = datetime.datetime.now().strftime('%Y%m%d_%H%M%S_%f')
		base_dir = os.path.dirname(os.path.abspath(__file__))
		path = os.path.join(base_dir, f"idmsa_debug_{prefix}_{ts}.log")
		with open(path, 'w', encoding='utf-8') as f:
			f.write(f"URL: {url}\n\n")
			f.write(f"Request headers:\n{headers}\n\n")
			f.write(f"Request json:\n{payload}\n\n")
			if resp is not None:
				f.write(f"Status: {resp.status_code}\n\n")
				f.write(f"Response headers:\n{dict(resp.headers)}\n\n")
				try:
					f.write(f"Response text:\n{resp.text[:4000]}\n")
				except Exception:
					pass
	except Exception:
		pass


def _extract_stk_from_html(html_text: str):
	try:
		# 直接查找 script#init_data
		m = re.search(r'<script[^>]*id=["\']init_data["\'][^>]*>(.*?)</script>', html_text, flags=re.DOTALL | re.IGNORECASE)
		if not m:
			return None
		text = m.group(1)
		data = json.loads(text)
		return data.get('meta', {}).get('h', {}).get('x-aos-stk')
	except Exception:
		return None


def fetch_stk_via_session(session: requests.Session, user_agent: str):
	"""登录完成后，用同一 session 后端请求 bag 页面以刷新拿到匹配当前会话的 STK。"""
	headers = {
		"User-Agent": user_agent,
		"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
		"Accept-Language": "zh-CN,zh;q=0.9",
		"Connection": "keep-alive",
		"Referer": "https://secure9.www.apple.com.cn",
	}
	try:
		resp = session.get("https://www.apple.com.cn/shop/bag", headers=headers, timeout=20)
		if resp.status_code == 200 and resp.text:
			stk = _extract_stk_from_html(resp.text)
			return stk
	except Exception:
		return None
	return None


def _post_secure9_grantcode(session: requests.Session, ssi_url: str, user_agent: str, stk: str = None) -> bool:
	"""模拟 HAR 中的 secure9 signIn 授权交接：POST grantCode= 到 ssi 页面（up=true）。"""
	try:
		if not ssi_url:
			return False
		# 规范化 up=true
		post_url = ssi_url
		if 'up=' not in post_url:
			sep = '&' if ('?' in post_url) else '?'
			post_url = f"{post_url}{sep}up=true"
		headers = {
			"User-Agent": user_agent,
			"Accept": "*/*",
			"Accept-Language": "zh-CN,zh;q=0.9",
			"Content-Type": "application/x-www-form-urlencoded",
			"Origin": "https://secure9.www.apple.com.cn",
			"Referer": ssi_url,
			"X-Requested-With": "Fetch",
			"modelVersion": "v2",
			"syntax": "graviton",
			"x-aos-model-page": "signInPage",
		}
		if stk:
			headers["x-aos-stk"] = stk
		resp = session.post(post_url, headers=headers, data="grantCode=", timeout=15)
		return resp.status_code == 200
	except Exception:
		return False


def _bridge_cookies_via_headless_browser(session: requests.Session, ssi_url: str, user_agent: str) -> None:
	"""使用无头浏览器完成 ssi 回联与 bag 加载，然后把浏览器里的 .apple.com.cn cookies 回灌到 requests.Session。"""
	try:
		opts = uc.ChromeOptions()
		opts.add_argument('--headless=new')
		opts.add_argument('--disable-gpu')
		opts.add_argument('--no-sandbox')
		opts.add_argument('--disable-dev-shm-usage')
		opts.add_argument('--disable-blink-features=AutomationControlled')
		opts.add_argument(f'--user-agent={user_agent}')
		drv = uc.Chrome(options=opts, use_subprocess=True)
		drv.set_page_load_timeout(25)
		drv.implicitly_wait(1)
		# 先到主域，便于注入 cookies
		drv.get("https://www.apple.com.cn/")
		# 将 session 的 cookies 注入浏览器（尽可能迁移）
		for c in list(session.cookies):
			try:
				domain = c.domain or '.apple.com.cn'
				if 'apple.com' in domain:
					drv.add_cookie({
						"name": c.name,
						"value": c.value,
						"domain": domain if 'apple.com.cn' in domain else '.apple.com.cn',
						"path": c.path or '/',
					})
			except Exception:
				continue
		# 访问 ssi 回联页与 bag
		if ssi_url:
			drv.get(ssi_url)
			time.sleep(0.6)
		drv.get("https://www.apple.com.cn/shop/bag")
		time.sleep(0.6)
		# 读取浏览器 cookies 回灌到 session
		br_cookies = drv.get_cookies()
		for ck in br_cookies:
			try:
				dm = ck.get('domain') or '.apple.com.cn'
				if 'apple.com' in dm:
					session.cookies.set(ck['name'], ck['value'], domain=dm, path=ck.get('path') or '/')
			except Exception:
				continue
		logger.info(f"桥接后会话Cookies: {[ck.name for ck in session.cookies]}")
		try:
			drv.quit()
		except Exception:
			pass
	except Exception as _:
		pass


def _extract_pltn_from_ssi_url(ssi_url: str):
	try:
		if not ssi_url:
			return None
		qs = parse_qs(urlparse(ssi_url).query)
		ssi = qs.get('ssi', [None])[0]
		if not ssi:
			return None
		decoded = unquote(ssi)
		m = re.search(r"pltn=([A-Za-z0-9_\-]+)", decoded)
		return m.group(1) if m else None
	except Exception:
		return None

def _secure9_checkout_start(session: requests.Session, user_agent: str, pltn: str = None):
	try:
		base = "https://secure9.www.apple.com.cn/shop/checkout/start"
		url = f"{base}?pltn={pltn}" if pltn else base
		headers = {
			"User-Agent": user_agent,
			"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
			"Accept-Language": "zh-CN,zh;q=0.9",
			"Referer": "https://secure9.www.apple.com.cn",
		}
		_ = session.get(url, headers=headers, allow_redirects=True, timeout=20)
		time.sleep(0.3)
	except Exception:
		pass


def _post_with_retries(session: requests.Session, url: str, headers: dict, payload: dict, max_attempts: int = 4, base_delay: float = 1.5) -> requests.Response:
	"""对登录相关POST接口增加503/5xx重试和指数退避。"""
	last_resp = None
	for attempt in range(1, max_attempts + 1):
		try:
			# 在尝试之间加入轻微随机延时，模拟真实用户节奏
			if attempt > 1:
				time.sleep(0.2 + random.uniform(0, 0.3))
			resp = session.post(url, headers=headers, json=payload, timeout=25)
			last_resp = resp
			if resp.status_code == 503 or resp.status_code >= 500:
				logger.warning(f"登录接口 {url} 返回 {resp.status_code}，第 {attempt}/{max_attempts} 次，将重试...")
				_dump_response_for_debug("5xx", url, headers, payload, resp)
				sleep_s = base_delay * (2 ** (attempt - 1)) + random.uniform(0.3, 1.2)
				time.sleep(sleep_s)
				continue
			return resp
		except Exception as ex:
			logger.warning(f"请求 {url} 出错: {ex}，第 {attempt}/{max_attempts} 次，将重试...")
			_dump_response_for_debug("exception", url, headers, payload, last_resp)
			sleep_s = base_delay * (2 ** (attempt - 1)) + random.uniform(0.3, 1.2)
			time.sleep(sleep_s)
	# 彻底失败时也转储一次
	_dump_response_for_debug("final_fail", url, headers, payload, last_resp)
	return last_resp




def clear_cart_if_needed(session, user_agent):
    """如果购物车中有商品，则清空购物车"""
    try:
        # 检查购物车状态
        status_url = "https://www.apple.com.cn/shop/bag/status?apikey=SJHJUH4YFCTTPD4F4"
        headers = {
            'User-Agent': user_agent,
            'Accept': 'application/json',
        }
        
        response = session.get(status_url, headers=headers, timeout=(10, 20))
        if response.status_code == 200:
            cart_data = response.json()
            item_count = cart_data.get('items', 0)
            
            if item_count > 0:
                logging.info(f"🛒 发现购物车中有 {item_count} 件商品，准备清空...")
                
                # 清空购物车 - 访问购物袋页面并移除所有商品
                bag_url = "https://www.apple.com.cn/shop/bag"
                _global_driver.get(bag_url)
                time.sleep(2)
                
                # 这里可以添加更复杂的清空逻辑，但简单起见，我们只是记录
                logging.info("💡 建议手动清空购物车或忽略购物车中的现有商品")
                return item_count
            else:
                logging.info("✅ 购物车为空，可以开始添加商品")
                return 0
    except Exception as e:
        logging.warning(f"⚠️ 检查购物车状态失败: {e}")
        return 0

def cleanup_driver(force_close=False):
    """清理全局浏览器实例"""
    global _global_driver
    if _global_driver:
        try:
            if force_close:
                _global_driver.quit()
                _global_driver = None
                logger.info("🔒 浏览器已关闭")
            else:
                logger.info("🌐 浏览器保持打开状态，请手动检查结果")
        except:
            pass

# --- 主执行逻辑 ---
if __name__ == "__main__":
    
    try:
        with open('add_to_bag/iphone_sku_links.json', 'r', encoding='utf-8') as f:
            sku_list = json.load(f)
        if not sku_list:
            print("错误: iphone_sku_links.json 为空或不存在，请先运行 iPhone_sku.py 生成它。")
            exit()
        
        # --- 在这里选择你想要抢购的产品 ---
        target_sku_info = sku_list[0] 
        # --------------------------------

    except FileNotFoundError:
        print("错误: 未找到 iphone_sku_links.json 文件。请先运行 iPhone_sku.py 来生成它。")
        exit()

    final_checkout_success = False
    
    try:
        MAX_ATTEMPTS = 1
        for attempt in range(1, MAX_ATTEMPTS + 1):
            logging.info(f"--- 开始第 {attempt}/{MAX_ATTEMPTS} 次尝试 ---")
            logging.info(f"目标产品: {target_sku_info['model']} {target_sku_info['color']} {target_sku_info['capacity']}")
            logging.info(f"产品页面: {target_sku_info['url']}")
            
            # 尝试使用保存的cookies，避免重复2FA
            token_info = get_atbtoken_stealth(target_sku_info['url'], use_saved_cookies=True)
            
            if token_info:
                # 检查并处理购物车中的现有商品
                try:
                    session = requests.Session()
                    for cookie_name, cookie_value in token_info.get('all_cookies', {}).items():
                        session.cookies.set(cookie_name, cookie_value)
                    
                    existing_items = clear_cart_if_needed(session, token_info['user_agent'])
                except Exception as e:
                    logging.warning(f"⚠️ 购物车检查失败: {e}")
                    existing_items = 0
                
                final_success = True
                for i in range(PURCHASE_QUANTITY):
                    # 如果购物车中已有商品，调整期望数量
                    expected_count = existing_items + i + 1
                    logging.info(f"正在添加第 {i + 1}/{PURCHASE_QUANTITY} 件商品... (期望总数: {expected_count})")
                    success = add_to_cart_backend(target_sku_info, token_info, expected_item_count=expected_count)
                    if not success:
                        logging.error(f"添加第 {i + 1} 件商品时失败。终止本次尝试。")
                        final_success = False
                        break
                
                if final_success:
                    logging.info(f"🎉 成功添加 {PURCHASE_QUANTITY} 件商品到购物车！")
                    logging.info("现在开始结账流程...")
                    
                    # 获取结账所需的数据
                    stk, item_ids = get_checkout_data_in_browser(_global_driver)
                    
                    if stk and item_ids:
                        # 创建结账会话
                        checkout_session = requests.Session()
                        all_browser_cookies = _global_driver.get_cookies()
                        for cookie in all_browser_cookies:
                            try:
                                checkout_session.cookies.set(cookie['name'], cookie['value'], domain=cookie.get('domain'))
                            except Exception:
                                pass
                        logging.info(f"已将 {len(all_browser_cookies)} 个浏览器 cookies 加载到最终结账 session 中。")

                        # 执行结账
                        final_checkout_success = checkout_improved(checkout_session, stk, item_ids, token_info['user_agent'])
                        # final_checkout_success = checkout_via_browser(_global_driver)
                        
                        if final_checkout_success:
                            logging.info("🎉 结账成功！正在跳转到支付页面...")
                            break
                        else:
                            logging.error("结账失败，将尝试重试...")
                    else:
                        logging.error("未能获取结账所需的数据（STK或商品ID）")
            else:
                logging.warning(f"第 {attempt} 次尝试失败，未能获取token。")

            if attempt < MAX_ATTEMPTS and not final_checkout_success:
                wait_time = 3
                logging.info(f"将在 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        else:
            logging.error(f"所有 {MAX_ATTEMPTS} 次尝试都失败了")

    except Exception as e:
        logging.error(f"执行过程中发生错误: {e}")
        print(f"\n>>> Error occurred: {e}")
    
    finally:
        if final_checkout_success:
            print(f"\n>>> 🎉 最终状态: 成功！请在浏览器中检查结果。")
            print(f">>> 💡 浏览器将保持打开状态，请手动确认购买完成情况。")
            cleanup_driver(force_close=False)  # 成功时不关闭浏览器
        else:
            print(f"\n>>> ❌ 最终状态: 失败。请查看上面的日志了解详情。")
            cleanup_driver(force_close=True)   # 失败时关闭浏览器
        
        logging.info("--- 苹果商店自动购买流程结束 ---")


