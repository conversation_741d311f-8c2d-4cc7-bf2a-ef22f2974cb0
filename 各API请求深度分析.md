# 各API请求深度分析

## 🎯 **模拟下单功能已实现**

✅ **修改完成**：代码现在会在到达"查看订单 — 安全结账"页面时停止，不会真实点击"立即下单"按钮，避免产生真实订单。

## 📋 **四个核心API详细分析**

### 1. **Fulfillment API - 地址选择**

#### 🔗 **请求URL**
```
POST /shop/checkoutx/fulfillment
```

#### 📊 **关键参数分析**
```python
# 核心字段
{
    "checkout.fulfillment.fulfillmentOptions.selectFulfillmentLocation": "HOME",
    "checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.state": "广东",
    "checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.city": "深圳", 
    "checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.district": "宝安区",
    "checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.provinceCityDistrict": "广东 深圳 宝安区",
    "checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.countryCode": "CN",
    "checkout.fulfillment.deliveryTab.delivery.shipmentGroups.shipmentGroup-1.shipmentOptionsGroups.shipmentOptionsGroup-1.shippingOptions.selectShippingOption": "A8"
}
```

#### 🎯 **功能说明**
- **目的**: 设置配送地址和配送方式
- **关键**: `provinceCityDistrict` 必须与用户账户地址匹配
- **A8含义**: 默认免费配送选项（白天配送）

#### ✅ **成功标志**
- 返回200状态码
- 响应JSON中 `page.url` 包含 `Shipping-init`
- 页面标题变为 "送货详情 — 安全结账"

---

### 2. **Shipping API - 配送方式确认**

#### 🔗 **请求URL**
```
POST /shop/checkoutx/shipping
```

#### 📊 **关键参数分析**
```python
# 从页面动态获取的配送相关数据
{
    "checkout.shipping.shippingOptions.selectShippingOption": "A8",
    "checkout.shipping.deliveryLocation.address.firstName": "少东",
    "checkout.shipping.deliveryLocation.address.lastName": "詹",
    "checkout.shipping.deliveryLocation.address.street": "松岗红湖路燕和苑",
    "checkout.shipping.deliveryLocation.address.street2": "a1号ZSD",
    "checkout.shipping.deliveryLocation.address.fullDaytimePhone": "••••••••0244",
    "checkout.shipping.deliveryLocation.address.emailAddress": "z••••••••••••••<EMAIL>"
}
```

#### 🎯 **功能说明**
- **目的**: 确认详细的配送信息和联系方式
- **关键**: 需要完整的收货人信息
- **数据来源**: 从页面的init_data中动态提取

#### ✅ **成功标志**
- 返回200状态码
- 页面跳转到 `Billing-init`
- 页面标题变为 "付款详情 — 安全结账"

---

### 3. **Billing API - 支付方式确认**

#### 🔗 **请求URL**
```
POST /shop/checkoutx/billing
```

#### 📊 **关键参数分析**
```python
# 支付相关数据
{
    "checkout.billing.billingOptions.selectBillingOption": "INSTALLMENT",
    "checkout.billing.installmentOptions.selectInstallmentOption": "CMB_24",
    "checkout.billing.billingLocation.address.firstName": "少东",
    "checkout.billing.billingLocation.address.lastName": "詹",
    "checkout.billing.billingLocation.address.emailAddress": "z••••••••••••••<EMAIL>"
}
```

#### 🎯 **功能说明**
- **目的**: 确认支付方式和账单地址
- **关键**: 支付方式选择（分期付款、信用卡等）
- **CMB_24**: 招商银行24期分期付款

#### ✅ **成功标志**
- 返回200状态码
- 页面跳转到 `Review-init`
- 页面标题变为 "查看订单 — 安全结账"

---

### 4. **Review API - 订单确认（模拟模式不执行）**

#### 🔗 **请求URL**
```
POST /shop/checkoutx/review
```

#### 📊 **关键参数分析**
```python
# 最终确认数据
{
    "checkout.review.placeOrder": "true",
    "checkout.review.termsAccepted": "true"
}
```

#### 🎯 **功能说明**
- **目的**: 提交最终订单
- **⚠️ 注意**: 模拟模式下不会执行此API
- **真实环境**: 会产生真实订单和扣款

---

## 🔧 **API请求的技术细节**

### 1. **请求头要求**
```python
headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) WebKit/537.36',
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
    'Referer': current_page_url,
    'Origin': base_url,
    'X-AOS-STK': current_stk_token  # 关键的安全令牌
}
```

### 2. **STK令牌机制**
- **获取**: 从页面的 `init_data` 中提取
- **格式**: `"x-aos-stk":"JiXtUBKPaTnnNI8p-9jlbQ4QYnw"`
- **作用**: 防止CSRF攻击，验证请求合法性
- **更新**: 每个API响应可能包含新的STK令牌

### 3. **数据格式**
- **编码**: `application/x-www-form-urlencoded`
- **结构**: 扁平化的键值对，使用点号分隔层级
- **示例**: `checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.state`

### 4. **响应处理**
```python
# 成功响应示例
{
    "head": {"status": 200},
    "body": {
        "meta": {
            "page": {
                "title": "送货详情 — 安全结账",
                "url": "/shop/checkoutx?_s=Shipping-init"
            }
        }
    }
}
```

## 🎯 **模拟下单功能特点**

### ✅ **安全保障**
- **不会产生真实订单**: 在查看订单页面停止
- **不会扣款**: 避免真实的支付操作
- **完整测试**: 验证整个流程直到最后一步

### 📊 **验证内容**
- **页面到达**: 确认成功到达查看订单页面
- **按钮可用**: 验证"立即下单"按钮存在且可点击
- **数据完整**: 保存最终页面用于分析

### 💾 **数据保存**
- **自动保存**: 最终页面HTML文件
- **时间戳**: 便于区分不同测试
- **路径**: `/Users/<USER>/Desktop/AOS API/final_review_page_YYYYMMDD_HHMMSS.html`

## 🚀 **使用建议**

### 1. **开发测试**
- 使用模拟模式验证流程
- 检查每个API的响应
- 确认数据格式正确

### 2. **生产环境**
- 可以选择性启用真实下单
- 添加额外的确认机制
- 记录完整的操作日志

### 3. **调试分析**
- 查看保存的HTML文件
- 分析API响应内容
- 验证数据传递正确性

这个模拟下单功能既保证了测试的完整性，又避免了真实订单的风险，是开发和测试的完美解决方案！
