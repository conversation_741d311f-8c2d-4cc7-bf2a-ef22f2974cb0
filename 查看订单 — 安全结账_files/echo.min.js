/*! For license information please see echo.min.js.LICENSE.txt */
var Echo;(()=>{var e={534:function(e,t,n){var i;!function(r,o){"use strict";var a="function",s="undefined",c="object",u="string",l="model",d="name",f="type",p="vendor",m="version",g="architecture",v="console",w="mobile",b="tablet",h="smarttv",y="wearable",E="embedded",T="Amazon",x="Apple",S="ASUS",k="BlackBerry",D="Google",R="Huawei",P="LG",_="Microsoft",C="Motorola",I="Samsung",M="Sharp",O="Sony",L="Xiaomi",N="Zebra",A="Facebook",B="Chromium OS",q="Mac OS",U=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},z=function(e,t){return typeof e===u&&-1!==F(t).indexOf(F(e))},F=function(e){return e.toLowerCase()},j=function(e,t){if(typeof e===u)return e=e.replace(/^\s\s*/,""),typeof t===s?e:e.substring(0,500)},$=function(e,t){for(var n,i,r,s,u,l,d=0;d<t.length&&!u;){var f=t[d],p=t[d+1];for(n=i=0;n<f.length&&!u&&f[n];)if(u=f[n++].exec(e))for(r=0;r<p.length;r++)l=u[++i],typeof(s=p[r])===c&&s.length>0?2===s.length?typeof s[1]==a?this[s[0]]=s[1].call(this,l):this[s[0]]=s[1]:3===s.length?typeof s[1]!==a||s[1].exec&&s[1].test?this[s[0]]=l?l.replace(s[1],s[2]):o:this[s[0]]=l?s[1].call(this,l,s[2]):o:4===s.length&&(this[s[0]]=l?s[3].call(this,l.replace(s[1],s[2])):o):this[s]=l||o;d+=2}},V=function(e,t){for(var n in t)if(typeof t[n]===c&&t[n].length>0){for(var i=0;i<t[n].length;i++)if(z(t[n][i],e))return"?"===n?o:n}else if(z(t[n],e))return"?"===n?o:n;return e},H={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},W={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[d,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[d,m],[/opios[\/ ]+([\w\.]+)/i],[m,[d,"Opera Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[m,[d,"Opera GX"]],[/\bopr\/([\w\.]+)/i],[m,[d,"Opera"]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[m,[d,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[d,m],[/\bddg\/([\w\.]+)/i],[m,[d,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[d,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[m,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[d,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[d,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[d,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[m,[d,"Smart Lenovo Browser"]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure Browser"],m],[/\bfocus\/([\w\.]+)/i],[m,[d,"Firefox Focus"]],[/\bopt\/([\w\.]+)/i],[m,[d,"Opera Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[d,"Opera Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[d,"MIUI Browser"]],[/fxios\/([-\w\.]+)/i],[m,[d,"Firefox"]],[/\bqihu|(qi?ho?o?|360)browser/i],[[d,"360 Browser"]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 Browser"],m],[/samsungbrowser\/([\w\.]+)/i],[m,[d,"Samsung Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],m],[/metasr[\/ ]?([\d\.]+)/i],[m,[d,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[d,"Sogou Mobile"],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[d,m],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[d],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[d,A],m],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[d,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[d,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[d,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[d,"Chrome Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[d,"Chrome WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[d,"Android Browser"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[d,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[d,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,d],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[d,[m,V,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[d,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[d,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[d,"Firefox Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[d,m],[/(cobalt)\/([\w\.]+)/i],[d,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,F]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,"",F]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,F]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[l,[p,I],[f,b]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[l,[p,I],[f,w]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[l,[p,x],[f,w]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[l,[p,x],[f,b]],[/(macintosh);/i],[l,[p,x]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[l,[p,M],[f,w]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[l,[p,R],[f,b]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[l,[p,R],[f,w]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[l,/_/g," "],[p,L],[f,w]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[l,/_/g," "],[p,L],[f,b]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[l,[p,"OPPO"],[f,w]],[/\b(opd2\d{3}a?) bui/i],[l,[p,"OPPO"],[f,b]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[l,[p,"Vivo"],[f,w]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[l,[p,"Realme"],[f,w]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[l,[p,C],[f,w]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[l,[p,C],[f,b]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[l,[p,P],[f,b]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[l,[p,P],[f,w]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[l,[p,"Lenovo"],[f,b]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[l,/_/g," "],[p,"Nokia"],[f,w]],[/(pixel c)\b/i],[l,[p,D],[f,b]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[l,[p,D],[f,w]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[l,[p,O],[f,w]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[l,"Xperia Tablet"],[p,O],[f,b]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[l,[p,"OnePlus"],[f,w]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[l,[p,T],[f,b]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[l,/(.+)/g,"Fire Phone $1"],[p,T],[f,w]],[/(playbook);[-\w\),; ]+(rim)/i],[l,p,[f,b]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[l,[p,k],[f,w]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[l,[p,S],[f,b]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[l,[p,S],[f,w]],[/(nexus 9)/i],[l,[p,"HTC"],[f,b]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[p,[l,/_/g," "],[f,w]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[l,[p,"Acer"],[f,b]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[l,[p,"Meizu"],[f,w]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[l,[p,"Ulefone"],[f,w]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[p,l,[f,w]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[p,l,[f,b]],[/(surface duo)/i],[l,[p,_],[f,b]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[l,[p,"Fairphone"],[f,w]],[/(u304aa)/i],[l,[p,"AT&T"],[f,w]],[/\bsie-(\w*)/i],[l,[p,"Siemens"],[f,w]],[/\b(rct\w+) b/i],[l,[p,"RCA"],[f,b]],[/\b(venue[\d ]{2,7}) b/i],[l,[p,"Dell"],[f,b]],[/\b(q(?:mv|ta)\w+) b/i],[l,[p,"Verizon"],[f,b]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[l,[p,"Barnes & Noble"],[f,b]],[/\b(tm\d{3}\w+) b/i],[l,[p,"NuVision"],[f,b]],[/\b(k88) b/i],[l,[p,"ZTE"],[f,b]],[/\b(nx\d{3}j) b/i],[l,[p,"ZTE"],[f,w]],[/\b(gen\d{3}) b.+49h/i],[l,[p,"Swiss"],[f,w]],[/\b(zur\d{3}) b/i],[l,[p,"Swiss"],[f,b]],[/\b((zeki)?tb.*\b) b/i],[l,[p,"Zeki"],[f,b]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[p,"Dragon Touch"],l,[f,b]],[/\b(ns-?\w{0,9}) b/i],[l,[p,"Insignia"],[f,b]],[/\b((nxa|next)-?\w{0,9}) b/i],[l,[p,"NextBook"],[f,b]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[p,"Voice"],l,[f,w]],[/\b(lvtel\-)?(v1[12]) b/i],[[p,"LvTel"],l,[f,w]],[/\b(ph-1) /i],[l,[p,"Essential"],[f,w]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[l,[p,"Envizen"],[f,b]],[/\b(trio[-\w\. ]+) b/i],[l,[p,"MachSpeed"],[f,b]],[/\btu_(1491) b/i],[l,[p,"Rotor"],[f,b]],[/(shield[\w ]+) b/i],[l,[p,"Nvidia"],[f,b]],[/(sprint) (\w+)/i],[p,l,[f,w]],[/(kin\.[onetw]{3})/i],[[l,/\./g," "],[p,_],[f,w]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[l,[p,N],[f,b]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[l,[p,N],[f,w]],[/smart-tv.+(samsung)/i],[p,[f,h]],[/hbbtv.+maple;(\d+)/i],[[l,/^/,"SmartTV"],[p,I],[f,h]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[p,P],[f,h]],[/(apple) ?tv/i],[p,[l,"Apple TV"],[f,h]],[/crkey/i],[[l,"Chromecast"],[p,D],[f,h]],[/droid.+aft(\w+)( bui|\))/i],[l,[p,T],[f,h]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[l,[p,M],[f,h]],[/(bravia[\w ]+)( bui|\))/i],[l,[p,O],[f,h]],[/(mitv-\w{5}) bui/i],[l,[p,L],[f,h]],[/Hbbtv.*(technisat) (.*);/i],[p,l,[f,h]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[p,j],[l,j],[f,h]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[f,h]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[p,l,[f,v]],[/droid.+; (shield) bui/i],[l,[p,"Nvidia"],[f,v]],[/(playstation [345portablevi]+)/i],[l,[p,O],[f,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[l,[p,_],[f,v]],[/((pebble))app/i],[p,l,[f,y]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[l,[p,x],[f,y]],[/droid.+; (glass) \d/i],[l,[p,D],[f,y]],[/droid.+; (wt63?0{2,3})\)/i],[l,[p,N],[f,y]],[/(quest( \d| pro)?)/i],[l,[p,A],[f,y]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[p,[f,E]],[/(aeobc)\b/i],[l,[p,T],[f,E]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[l,[f,w]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[l,[f,b]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[f,b]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[f,w]],[/(android[-\w\. ]{0,9});.+buil/i],[l,[p,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[d,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,d]],os:[[/microsoft (windows) (vista|xp)/i],[d,m],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[d,[m,V,H]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[m,V,H],[d,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[d,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[d,q],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,d],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[d,m],[/\(bb(10);/i],[m,[d,k]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[d,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[d,"Firefox OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[d,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[d,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[d,"Chromecast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[d,B],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[d,m],[/(sunos) ?([\w\.\d]*)/i],[[d,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[d,m]]},G=function(e,t){if(typeof e===c&&(t=e,e=o),!(this instanceof G))return new G(e,t).getResult();var n=typeof r!==s&&r.navigator?r.navigator:o,i=e||(n&&n.userAgent?n.userAgent:""),l=n&&n.userAgentData?n.userAgentData:o,d=t?function(e,t){var n={};for(var i in e)t[i]&&t[i].length%2==0?n[i]=t[i].concat(e[i]):n[i]=e[i];return n}(W,t):W,f=n&&n.userAgent==i;return this.getBrowser=function(){var e,t={};return t.name=o,t.version=o,$.call(t,i,d.browser),t.major=typeof(e=t.version)===u?e.replace(/[^\d\.]/g,"").split(".")[0]:o,f&&n&&n.brave&&typeof n.brave.isBrave==a&&(t.name="Brave"),t},this.getCPU=function(){var e={};return e.architecture=o,$.call(e,i,d.cpu),e},this.getDevice=function(){var e={};return e.vendor=o,e.model=o,e.type=o,$.call(e,i,d.device),f&&!e.type&&l&&l.mobile&&(e.type=w),f&&"Macintosh"==e.model&&n&&typeof n.standalone!==s&&n.maxTouchPoints&&n.maxTouchPoints>2&&(e.model="iPad",e.type=b),e},this.getEngine=function(){var e={};return e.name=o,e.version=o,$.call(e,i,d.engine),e},this.getOS=function(){var e={};return e.name=o,e.version=o,$.call(e,i,d.os),f&&!e.name&&l&&l.platform&&"Unknown"!=l.platform&&(e.name=l.platform.replace(/chrome os/i,B).replace(/macos/i,q)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===u&&e.length>500?j(e,500):e,this},this.setUA(i),this};G.BROWSER=U([d,m,"major"]),G.CPU=U([g]),G.DEVICE=U([l,p,f,v,w,h,b,y,E]),G.ENGINE=G.OS=U([d,m]),typeof t!==s?(e.exports&&(t=e.exports=G),t.UAParser=G):n.amdO?(i=function(){return G}.call(t,n,t,e))===o||(e.exports=i):typeof r!==s&&(r.UAParser=G);var J=typeof r!==s&&(r.jQuery||r.Zepto);if(J&&!J.ua){var Z=new G;J.ua=Z.getResult(),J.ua.get=function(){return Z.getUA()},J.ua.set=function(e){Z.setUA(e);var t=Z.getResult();for(var n in t)J.ua[n]=t[n]}}}("object"==typeof window?window:this)}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var o=t[i]={exports:{}};return e[i].call(o.exports,o,o.exports,n),o.exports}n.amdO={},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(534),t=n.n(e);const i="echo_session_start_timestamp",r="alt_text_collection",o="cumulative_layout_shift",a="custom_event",s="error",c="interaction",u="interaction_to_next_paint",l="largest_contentful_paint",d="navigation_timing",f="paint",p="performance_measure",m="performance_now",g="time_to_first_byte",v="unload",w="visibility_change";let b={passive:[],critical:[]};function h(){b={passive:[],critical:[]}}function y({config:e,events:t=[]}){t.forEach((t=>{const n=e.ingestionEndpointMap[t.eventType];n&&!0===n.passive&&b.passive.push(t),n&&!0===n.critical&&b.critical.push(t)}))}function E(){const e=[],t=t=>e.find((e=>e.fn===t||e.key===t));return{add:t=>e.push({...t,active:!1!==t.active,set:function(e,t){this[e]=t}}),set:(e,n,i)=>{const r=t(e);r&&r.set(n,i)},mergeConfig:e=>{Object.keys(e).forEach((n=>{const i=t(n);i&&(!i.data&&e[n]&&i.set("active",!0),i.set("data",e[n]))}))},serialize:()=>e.filter((e=>e.active)).map((e=>[e.fn,e.data])),dump:()=>e}}function T(e){if(!e)return null;const t=new URL(e);return t.search="query",t.href}const x=e=>({...e,...e.pageUrl&&{pageUrl:T(e.pageUrl)},...e.referer&&{referer:T(e.referer)},...e.errorUrl&&{errorUrl:T(e.errorUrl)}}),S=({config:e,eventType:t,eventData:n})=>({app:e.app,eventType:t,postTime:Date.now(),...e.eventDataDefaults,...n,audit:{...n.audit,...e.bundleDecoration},...e.pageViewId&&{pageViewId:e.pageViewId}});function k({config:e,eventType:t,eventData:n}){const i=E();!function(e,t){const n=t.transforms||[];"function"==typeof t.sanitizeEventData&&n.push(t.sanitizeEventData),n.forEach((n=>e.add({fn:n,data:t})))}(i,e),i.add({fn:x});const r=i.serialize();return(o=r,function(e){let t=e;for(let e=o.length-1;e>=0;e--){const[n,i]=o[e];if(t=n(t,i),!1===t)return!1}return t})({eventType:t,eventData:n});var o}function D({storage:e=sessionStorage,pct:t=0}={}){const n=e&&e.getItem("echo:altTextEventSampleRatePct");return n?Number(n):t}function R({storage:e=sessionStorage,pct:t=0}={}){const n=e&&e.getItem("echo:customEventSampleRatePct");return n?Number(n):t}function P({storage:e=sessionStorage,pct:t=0}={}){const n=e&&e.getItem("echo:interactionEventSampleRatePct");return n?Number(n):t}function _({storage:e=sessionStorage,pct:t=0}={}){const n=e&&e.getItem("echo:logEventSampleRatePct");return n?Number(n):t}function C({storage:e=sessionStorage,pct:t=0}={}){const n=e&&e.getItem("echo:performanceMeasureEventSampleRatePct");return n?Number(n):t}function I({storage:e=sessionStorage,pct:t=0}={}){const n=e&&e.getItem("echo:performanceNowEventSampleRatePct");return n?Number(n):t}function M({storage:e=sessionStorage,pct:t=0}={}){const n=e&&e.getItem("echo:rumEventSampleRatePct")||e.getItem("echo:nonEssentialEventSampleRatePct");return n?Number(n):t}function O({suppliedRand:e,rate:t=100}){const n=100*Math.random();return(e||n)<=t}function L(e,t){try{return e()}catch(e){return t&&y({config:t,events:[k({config:t,eventType:s,eventData:{errorMsg:e.message}})]}),null}}function N(e){if(!e)return{};const t=new URL(e);return t.protocol.startsWith("http")||(t.pathname="pathname"),t}const A=[!0,"true",1,"1"],B={url:null,passiveEventIngestionUrl:null,criticalEventIngestionUrl:null,sendErrors:!1,sendPageViewData:!1,delaySendingPageViewDataMS:500,logRequests:("echo:logRequests",L((()=>A.includes(sessionStorage.getItem("echo:logRequests"))))),performanceMeasurePollingIntervalMS:1e3,sendResourceData:!1,resourceDisallowedResourceList:[],resourcePollingIntervalMS:2e3,altTextEventSampleRatePct:0,customEventSampleRatePct:0,interactionEventSampleRatePct:0,logEventSampleRatePct:0,nonEssentialEventSampleRatePct:0,performanceMeasureEventSampleRatePct:0,performanceNowEventSampleRatePct:0,resourceEventSampleRatePct:0,rumEventSampleRatePct:0,ingestionEndpointMap:{[r]:{passive:!0,critical:!0},[o]:{passive:!0,critical:!0},[a]:{passive:!0,critical:!0},echoFeatureEvent:{passive:!0,critical:!0},[s]:{passive:!1,critical:!0},first_input_delay:{passive:!0,critical:!0},[c]:{passive:!1,critical:!0},[u]:{passive:!0,critical:!0},[l]:{passive:!0,critical:!0},log:{passive:!0,critical:!0},network_error:{passive:!0,critical:!0},[d]:{passive:!0,critical:!0},[f]:{passive:!0,critical:!0},[p]:{passive:!0,critical:!0},[m]:{passive:!0,critical:!0},resource:{passive:!0,critical:!0},[g]:{passive:!0,critical:!0},[v]:{passive:!0,critical:!0},[w]:{passive:!0,critical:!0}},transforms:[e=>e]};const q={pageViewId:`${window.crypto&&window.crypto.getRandomValues?([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(e=>(e^window.crypto.getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16))):"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return`${("x"==e?t:3&t|8).toString(16)}-legacy`}))}`,sessionStartTS:function(){const e=L((()=>sessionStorage.getItem(i)));return null!==e?parseInt(e,10):function(){const e=Date.now();return L((()=>sessionStorage.setItem(i,`${e}`))),e}()}()},U={sent:!1,triggeredBy:""};let z=null;const F=({crypto:e={}})=>({exists:!!e.subtle}),j=({navigator:e={},NetworkInformation:t})=>({exists:!("function"!=typeof t||"object"!=typeof e.connection)}),$=(e,t)=>t&&(t.supportedEntryTypes||[]).includes(e),V=e=>{const t={exists:!1};if(!e.performance)return t;t.exists=!0,t.timeline=0;const n=e.PerformanceObserver;return n&&(t.timeline=1.5,"function"==typeof new n((()=>{})).takeRecords&&(t.timeline=2)),t.hasMeasures="function"==typeof e.performance.getEntriesByType&&"function"==typeof e.performance.clearMeasures&&$("measure",n),t.navigationTiming=0,$("navigation",n)?t.navigationTiming=2:e.performance.timing&&(t.navigationTiming=1),t.resourceTiming=0,$("resource",n)&&(t.resourceTiming=2),t.hasResourceTimingBuffer="function"==typeof e.performance.clearResourceTimings,t};function H(e=window){return z||(z={crypto:F(e),networkInfo:j(e),perf:V(e)}),z}function W({config:e}){const t=function({config:e}){if(!H().perf.hasMeasures)return[];const t=window.performance.getEntriesByType("measure").filter((e=>e.name.startsWith("echo-")));return t.forEach((({name:e})=>window.performance.clearMeasures(e))),t.map((t=>k({config:e,eventType:p,eventData:{...t.toJSON(),sampleRate:e.performanceMeasureEventSampleRatePct}})))}({config:e});return t.length>0&&y({config:e,events:[t]})}function G({config:e}){function t(){return W({config:e})}return{start:()=>{H().perf.hasMeasures&&window.setInterval((()=>W({config:e})),e.performanceMeasurePollingIntervalMS),H().perf.hasResourceTimingBuffer&&window.addEventListener("resourcetimingbufferfull",t)}}}const J=({config:e})=>{const t={};function n(n){const i=N(n.filename),r=function({event:e}){return{message:e.error&&e.error.message||e.message,colno:e.colno,lineno:e.lineno,name:e.error&&e.error.name||"Error",stack:e.error&&e.error.stack,performanceNow:window.performance&&window.performance.now?window.performance.now():"",performanceTimeOrigin:window.performance?window.performance.timeOrigin:""}}({event:n});r.errorUrl=i.href,r.filename=i.href,r.id="echo/uncaught-error",r.type="error";const o=`${r.message} @ ${r.filename}:${r.lineno}:${r.colno}`,a=t[o]||0;if(t[o]=a+1,a>0)return window.console&&window.console.log(`Repeated error (${a} times): ${o}`);const c=k({config:e,eventType:s,eventData:r});return window.console&&window.console.error("Error:",c),y({config:e,events:[c]})}return{start:()=>window.addEventListener("error",n)}};function Z({config:e}){function t(t){const n=k({config:e,eventType:a,eventData:{...t.detail,sampleRate:e.customEventSampleRatePct}});return y({config:e,events:[n]})}const n="echoCustomEvent";return{start:()=>window.addEventListener(n,t),stop:()=>window.removeEventListener(n,t)}}function X({config:e}){function t(t){const{id:n}=t.detail,i=k({config:e,eventType:c,eventData:{id:n,timeOrigin:performance.timeOrigin,performanceNow:performance.now(),sampleRate:e.interactionEventSampleRatePct}});return y({config:e,events:[i]})}const n="echoInteractionEvent";return{start:()=>window.addEventListener(n,t),stop:()=>window.removeEventListener(n,t)}}function Y({config:e}){function t(t){const n=t.detail&&"error"===t.detail.type?s:"log";{const i=k({config:e,eventType:n,eventData:t.detail});return y({config:e,events:[i]})}}const n="echoLogEvent";return{start:()=>window.addEventListener(n,t),stop:()=>window.removeEventListener(n,t)}}function K({config:e}){function t(t){const{id:n,performanceNow:i,meta:r={}}=t.detail,o=k({config:e,eventType:"performance_now",eventData:{id:n,performanceNow:i,time:i,meta:r,message:r,sampleRate:e.performanceNowEventSampleRatePct}});return y({config:e,events:[o]})}const n="echoPerformanceNowEvent";return{start:()=>window.addEventListener(n,t),stop:()=>window.removeEventListener(n,t)}}var Q,ee,te,ne=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},ie=function(e){if("loading"===document.readyState)return"loading";var t=ne();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"},re=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},oe=function(e,t){var n="";try{for(;e&&9!==e.nodeType;){var i=e,r=i.id?"#"+i.id:re(i)+(i.classList&&i.classList.value&&i.classList.value.trim()&&i.classList.value.trim().length?"."+i.classList.value.trim().replace(/\s+/g,"."):"");if(n.length+r.length>(t||100)-1)return n||r;if(n=n?r+">"+n:r,i.id)break;e=i.parentNode}}catch(e){}return n},ae=-1,se=function(){return ae},ce=function(e){addEventListener("pageshow",(function(t){t.persisted&&(ae=t.timeStamp,e(t))}),!0)},ue=function(){var e=ne();return e&&e.activationStart||0},le=function(e,t){var n=ne(),i="navigate";return se()>=0?i="back-forward-cache":n&&(document.prerendering||ue()>0?i="prerender":document.wasDiscarded?i="restore":n.type&&(i=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},de=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},n||{})),i}}catch(e){}},fe=function(e,t,n,i){var r,o;return function(a){t.value>=0&&(a||i)&&((o=t.value-(r||0))||void 0===r)&&(r=t.value,t.delta=o,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},pe=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},me=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},ge=function(e){var t=!1;return function(){t||(e(),t=!0)}},ve=-1,we=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},be=function(e){"hidden"===document.visibilityState&&ve>-1&&(ve="visibilitychange"===e.type?e.timeStamp:0,ye())},he=function(){addEventListener("visibilitychange",be,!0),addEventListener("prerenderingchange",be,!0)},ye=function(){removeEventListener("visibilitychange",be,!0),removeEventListener("prerenderingchange",be,!0)},Ee=function(){return ve<0&&(ve=we(),he(),ce((function(){setTimeout((function(){ve=we(),he()}),0)}))),{get firstHiddenTime(){return ve}}},Te=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},xe=[1800,3e3],Se=function(e,t){t=t||{},Te((function(){var n,i=Ee(),r=le("FCP"),o=de("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<i.firstHiddenTime&&(r.value=Math.max(e.startTime-ue(),0),r.entries.push(e),n(!0)))}))}));o&&(n=fe(e,r,xe,t.reportAllChanges),ce((function(i){r=le("FCP"),n=fe(e,r,xe,t.reportAllChanges),pe((function(){r.value=performance.now()-i.timeStamp,n(!0)}))})))}))},ke=[.1,.25],De=0,Re=1/0,Pe=0,_e=function(e){e.forEach((function(e){e.interactionId&&(Re=Math.min(Re,e.interactionId),Pe=Math.max(Pe,e.interactionId),De=Pe?(Pe-Re)/7+1:0)}))},Ce=function(){return Q?De:performance.interactionCount||0},Ie=function(){"interactionCount"in performance||Q||(Q=de("event",_e,{type:"event",buffered:!0,durationThreshold:0}))},Me=[],Oe=new Map,Le=0,Ne=[],Ae=function(e){if(Ne.forEach((function(t){return t(e)})),e.interactionId||"first-input"===e.entryType){var t=Me[Me.length-1],n=Oe.get(e.interactionId);if(n||Me.length<10||e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0].startTime&&n.entries.push(e);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};Oe.set(i.id,i),Me.push(i)}Me.sort((function(e,t){return t.latency-e.latency})),Me.length>10&&Me.splice(10).forEach((function(e){return Oe.delete(e.id)}))}}},Be=function(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=ge(e),"hidden"===document.visibilityState?e():(n=t(e),me(e)),n},qe=[200,500],Ue=[],ze=[],Fe=new WeakMap,je=new Map,$e=-1,Ve=function(e){Ue=Ue.concat(e),He()},He=function(){$e<0&&($e=Be(We))},We=function(){je.size>10&&je.forEach((function(e,t){Oe.has(t)||je.delete(t)}));var e=Me.map((function(e){return Fe.get(e.entries[0])})),t=ze.length-50;ze=ze.filter((function(n,i){return i>=t||e.includes(n)}));for(var n=new Set,i=0;i<ze.length;i++){var r=ze[i];Ge(r.startTime,r.processingEnd).forEach((function(e){n.add(e)}))}for(var o=0;o<50;o++){var a=Ue[Ue.length-1-o];if(!a||a.startTime<te)break;n.add(a)}Ue=Array.from(n),$e=-1};Ne.push((function(e){e.interactionId&&e.target&&!je.has(e.interactionId)&&je.set(e.interactionId,e.target)}),(function(e){var t,n=e.startTime+e.duration;te=Math.max(te,e.processingEnd);for(var i=ze.length-1;i>=0;i--){var r=ze[i];if(Math.abs(n-r.renderTime)<=8){(t=r).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:n,entries:[e]},ze.push(t)),(e.interactionId||"first-input"===e.entryType)&&Fe.set(e,t),He()}));var Ge=function(e,t){for(var n,i=[],r=0;n=Ue[r];r++)if(!(n.startTime+n.duration<e)){if(n.startTime>t)break;i.push(n)}return i},Je=[2500,4e3],Ze={},Xe=[800,1800],Ye=function e(t){document.prerendering?Te((function(){return e(t)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(t)}),!0):setTimeout(t,0)},Ke=function(e,t){t=t||{};var n=le("TTFB"),i=fe(e,n,Xe,t.reportAllChanges);Ye((function(){var r=ne();r&&(n.value=Math.max(r.responseStart-ue(),0),n.entries=[r],i(!0),ce((function(){n=le("TTFB",0),(i=fe(e,n,Xe,t.reportAllChanges))(!0)})))}))};function Qe({config:e}){ee||(ee=de("long-animation-frame",Ve)),function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},Te((function(){var n;Ie();var i,r=le("INP"),o=function(e){Be((function(){e.forEach(Ae);var t=function(){var e=Math.min(Me.length-1,Math.floor((Ce()-Le)/50));return Me[e]}();t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,i())}))},a=de("event",o,{durationThreshold:null!==(n=t.durationThreshold)&&void 0!==n?n:40});i=fe(e,r,qe,t.reportAllChanges),a&&(a.observe({type:"first-input",buffered:!0}),me((function(){o(a.takeRecords()),i(!0)})),ce((function(){Le=Ce(),Me.length=0,Oe.clear(),r=le("INP"),i=fe(e,r,qe,t.reportAllChanges)})))})))}((function(t){var n=function(e){var t=e.entries[0],n=Fe.get(t),i=t.processingStart,r=n.processingEnd,o=n.entries.sort((function(e,t){return e.processingStart-t.processingStart})),a=Ge(t.startTime,r),s=e.entries.find((function(e){return e.target})),c=s&&s.target||je.get(t.interactionId),u=[t.startTime+t.duration,r].concat(a.map((function(e){return e.startTime+e.duration}))),l=Math.max.apply(Math,u),d={interactionTarget:oe(c),interactionTargetElement:c,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:l,processedEventEntries:o,longAnimationFrameEntries:a,inputDelay:i-t.startTime,processingDuration:r-i,presentationDelay:Math.max(l-r,0),loadState:ie(t.startTime)};return Object.assign(e,{attribution:d})}(t);(t=>{const n={inputDelay:t?.attribution?.inputDelay,interactionTarget:t?.attribution?.interactionTarget,interactionTime:t?.attribution?.interactionTime,interactionType:t?.attribution?.interactionType,loadState:t?.attribution?.loadState,nextPaintTime:t?.attribution?.nextPaintTime,presentationDelay:t?.attribution?.presentationDelay};delete t.entries;const i=k({config:e,eventType:u,eventData:{...t,attribution:n}});y({config:e,events:[i]})})(n)}),void 0),Se((function(t){var n=function(e){var t={timeToFirstByte:0,firstByteToFCP:e.value,loadState:ie(se())};if(e.entries.length){var n=ne(),i=e.entries[e.entries.length-1];if(n){var r=n.activationStart||0,o=Math.max(0,n.responseStart-r);t={timeToFirstByte:o,firstByteToFCP:e.value-o,loadState:ie(e.entries[0].startTime),navigationEntry:n,fcpEntry:i}}}return Object.assign(e,{attribution:t})}(t);(t=>{delete t.attribution,delete t.entries;const n=k({config:e,eventType:f,eventData:{...t,startTime:t.value}});y({config:e,events:[n]})})(n)}),void 0),Ke((function(t){var n=function(e){var t={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(e.entries.length){var n=e.entries[0],i=n.activationStart||0,r=Math.max((n.workerStart||n.fetchStart)-i,0),o=Math.max(n.domainLookupStart-i,0),a=Math.max(n.connectStart-i,0),s=Math.max(n.connectEnd-i,0);t={waitingDuration:r,cacheDuration:o-r,dnsDuration:a-o,connectionDuration:s-a,requestDuration:e.value-s,navigationEntry:n}}return Object.assign(e,{attribution:t})}(t);(t=>{const n={waitingDuration:t?.attribution?.waitingDuration,cacheDuration:t?.attribution?.cacheDuration,dnsDuration:t?.attribution?.dnsDuration,connectionDuration:t?.attribution?.connectionDuration,requestDuration:t?.attribution?.requestDuration};delete t.entries;const i=k({config:e,eventType:g,eventData:{...t,attribution:n}});y({config:e,events:[i]})})(n)}),void 0),function(e,t){t=t||{},Te((function(){var n,i=Ee(),r=le("LCP"),o=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<i.firstHiddenTime&&(r.value=Math.max(e.startTime-ue(),0),r.entries=[e],n())}))},a=de("largest-contentful-paint",o);if(a){n=fe(e,r,Je,t.reportAllChanges);var s=ge((function(){Ze[r.id]||(o(a.takeRecords()),a.disconnect(),Ze[r.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return Be(s)}),!0)})),me(s),ce((function(i){r=le("LCP"),n=fe(e,r,Je,t.reportAllChanges),pe((function(){r.value=performance.now()-i.timeStamp,Ze[r.id]=!0,n(!0)}))}))}}))}((function(t){var n=function(e){var t={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:e.value};if(e.entries.length){var n=ne();if(n){var i=n.activationStart||0,r=e.entries[e.entries.length-1],o=r.url&&performance.getEntriesByType("resource").filter((function(e){return e.name===r.url}))[0],a=Math.max(0,n.responseStart-i),s=Math.max(a,o?(o.requestStart||o.startTime)-i:0),c=Math.max(s,o?o.responseEnd-i:0),u=Math.max(c,r.startTime-i);t={element:oe(r.element),timeToFirstByte:a,resourceLoadDelay:s-a,resourceLoadDuration:c-s,elementRenderDelay:u-c,navigationEntry:n,lcpEntry:r},r.url&&(t.url=r.url),o&&(t.lcpResourceEntry=o)}}return Object.assign(e,{attribution:t})}(t);(t=>{const n={element:t?.attribution?.element,elementRenderDelay:t?.attribution?.elementRenderDelay,lcpEntry:t?.attribution?.lcpEntry,resourceLoadDelay:t?.attribution?.resourceLoadDelay,resourceLoadDuration:t?.attribution?.resourceLoadDuration,timeToFirstByte:t?.attribution?.timeToFirstByte,url:t?.attribution?.url};delete t.entries;const i=k({config:e,eventType:l,eventData:{...t,attribution:n}});y({config:e,events:[i]})})(n)}),void 0),function(e,t){t=t||{},Se(ge((function(){var n,i=le("CLS",0),r=0,o=[],a=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=o[0],n=o[o.length-1];r&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(r+=e.value,o.push(e)):(r=e.value,o=[e])}})),r>i.value&&(i.value=r,i.entries=o,n())},s=de("layout-shift",a);s&&(n=fe(e,i,ke,t.reportAllChanges),me((function(){a(s.takeRecords()),n(!0)})),ce((function(){r=0,i=le("CLS",0),n=fe(e,i,ke,t.reportAllChanges),pe((function(){return n()}))})),setTimeout(n,0))})))}((function(t){var n=function(e){var t,n={};if(e.entries.length){var i=e.entries.reduce((function(e,t){return e&&e.value>t.value?e:t}));if(i&&i.sources&&i.sources.length){var r=(t=i.sources).find((function(e){return e.node&&1===e.node.nodeType}))||t[0];r&&(n={largestShiftTarget:oe(r.node),largestShiftTime:i.startTime,largestShiftValue:i.value,largestShiftSource:r,largestShiftEntry:i,loadState:ie(i.startTime)})}}return Object.assign(e,{attribution:n})}(t);(t=>{const n={largestShiftTarget:t?.attribution?.largestShiftTarget,largestShiftTime:t?.attribution?.largestShiftTime,largestShiftValue:t?.attribution?.largestShiftValue,loadState:t?.attribution?.loadState};delete t.entries;const i=k({config:e,eventType:o,eventData:{...t,attribution:n}});y({config:e,events:[i]})})(n)}),void 0)}new Date;const et=new Set(["connectEnd","connectStart","domainLookupEnd","domainLookupStart","domComplete","domContentLoadedEventEnd","domContentLoadedEventStart","domInteractive","domLoading","fetchStart","loadEventEnd","loadEventStart","requestStart","responseEnd","responseStart"]),tt=new Set(["redirectEnd","redirectStart","secureConnectionStart","unloadEventEnd","unloadEventStart",...et]);function nt(e=H(),t=window){const n=e.perf;let i={};if(!n.exists)return i;if(2===n.navigationTiming){const e=t.performance.getEntriesByType("navigation");e.length>0&&"function"==typeof e[0].toJSON&&(i={...e[0].toJSON(),NavigationTimingLevel:2})}else if(1===n.navigationTiming){const e=t.performance.timing,n=Object.keys(e).filter((e=>tt.has(e))).reduce(((t,n)=>et.has(n)?{...t,[n]:e[n]-e.navigationStart}:{...t,[n]:e[n]}),{});i={...n,NavigationTimingLevel:1}}return e.networkInfo.exists&&(i.networkDownlink=t.navigator.connection.downlink||null,i.networkEffectiveType=t.navigator.connection.effectiveType||null,i.networkRtt=t.navigator.connection.rtt||null),i.imageCountOnLoad=document.images.length,i.support={subtleCrypto:e.crypto.exists},i}function it({url:e,bundle:t,keepalive:n=!1}){return new Request(e,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify(t),keepalive:n})}function rt({config:e}){if(!1===e.flags.sent){e.sendRumEvents&&function({config:e}){const t=function({config:e,eventType:t="navigation_timing"}){const n={...nt()},i=N(document.referrer);return n.referer=i.href,n.referrer=i.href,k({config:e,eventType:t,eventData:n})}({config:e});y({config:e,events:[t]})}({config:e});const{passive:t,critical:n}=b;[{url:e.passiveEventIngestionUrl||e.url,batch:t},{url:e.criticalEventIngestionUrl,batch:n}].forEach((t=>{if(t.batch.length>0){const n=function({config:e,batch:t}){const n=(({config:e={},events:t=[]}={})=>{try{const n={deliveryVersion:"1.0",postTime:Date.now(),events:[S({config:e,eventType:"event_list",eventData:{audit:{triggeredBy:e.flags&&e.flags.triggeredBy,eventList:t.sort(((e,t)=>e.eventType.localeCompare(t.eventType)))}}})]};return n}catch(e){return console.error(e)}})({config:e,events:t});return h(),function({config:e,bundle:t}){!0===e.logRequests&&(console.info("%c/** ------------ Echo bundle begin ------------","color: skyblue"),console.info(`sent  to ${e.url}`),console.info(`sent at ${new Date(t.postTime)}`),t.events[0]&&t.events[0].audit.eventList.forEach((e=>{console.info({[e.eventType]:e})})),console.info("%c------------ Echo bundle end ------------ **/","color: skyblue"))}({config:e,bundle:n}),n}({config:e,batch:t.batch});return async function({config:e,bundle:t,url:n}){return"keepalive"in new Request("")?function({bundle:e,url:t}){return fetch(it({url:t,bundle:e,keepalive:!0}))}({config:e,bundle:t,url:n}):navigator.sendBeacon?function({bundle:e,url:t}){return navigator.sendBeacon(t,new Blob([JSON.stringify(e)],{type:"application/json; charset=UTF-8"}))}({config:e,bundle:t,url:n}):function({bundle:e,url:t}){return fetch(it({url:t,bundle:e}))}({config:e,bundle:t,url:n})}({config:e,bundle:n,url:t.url})}})),e.flags.sent=!0}return!1}let ot=new Set;const at=({config:e})=>{function t(){"hidden"===document.visibilityState&&(e.flags.triggeredBy=w,function({config:e}){ot.size>0&&(y({config:e,events:[k({config:e,eventType:r,eventData:{audit:{imageData:Array.from(ot)},sampleRate:e.altTextEventSampleRatePct}})]}),ot.clear())}({config:e}),setTimeout((()=>rt({config:e})),0))}return e.sendRumEvents&&Qe({config:e}),{start:()=>{document.addEventListener("visibilitychange",t,!0)}}},st=({config:e})=>{function t(){return e.flags.triggeredBy=v,rt({config:e})}return{start:()=>window.addEventListener("unload",t)}};let ct;function ut({config:e={}}){if(!e.url||!e.app)throw new Error("Missing config.url or config.app");const t=L((()=>JSON.parse(sessionStorage.getItem("echo_config"))))||{},n={...B,...e,...t,eventDataDefaults:q,flags:U};return n.sendAltTextEvents=O({rate:D({pct:n.altTextEventSampleRatePct})}),n.sendCustomEvents=O({rate:R({pct:n.customEventSampleRatePct})}),n.sendInteractionEvents=O({rate:P({pct:n.interactionEventSampleRatePct})}),n.sendLogEvents=O({rate:_({pct:n.logEventSampleRatePct})}),n.sendPerformanceMeasureEvents=O({rate:C({pct:n.performanceMeasureEventSampleRatePct})}),n.sendPerformanceNowEvents=O({rate:I({pct:n.performanceNowEventSampleRatePct})}),n.sendRumEvents=O({rate:M({pct:n.rumEventSampleRatePct||n.nonEssentialEventSampleRatePct})}),n.sendRumEvents&&(n.sendCustomEvents&&ct.set(Z,"active",!0),n.sendPerformanceMeasureEvents&&ct.set(G,"active",!0),n.sendPerformanceNowEvents&&ct.set(K,"active",!0)),n.sendInteractionEvents&&ct.set(X,"active",!0),ct.set(Y,"active",!0),ct.mergeConfig(n),ct.serialize().forEach((([e,t])=>{const i=e({config:n});void 0!==t&&!0!==t||i.start()})),{version:"3.28.0"}}ut.version="3.28.0",ut.listen=e=>(Array.isArray(e)?e.forEach((e=>ct.add(e))):ct.add(e),ut),(ut.setupListeners=()=>{ct=E(),ut.listen([{fn:at},{fn:st},{fn:Y},{fn:J,key:"sendErrors",data:!1},{fn:Z,active:!1},{fn:X,active:!1},{fn:G,active:!1},{fn:K,active:!1}])})();const lt=ut,dt=(e="")=>{let t=null;const n=(document.cookie||"").split(";");for(let i=0;i<n.length;i++){const r=(n[i]||"").trim();if(r.substring(0,e.length+1)===e+"="){t=decodeURIComponent(r.substring(e.length+1));break}}if(t)try{t=JSON.parse(t)}catch(e){}return t},ft=()=>{const e="echoUaParserData";try{let n;const i=sessionStorage.getItem(e);if(i)return n=JSON.parse(i),n;const r=new(t())(window.navigator.userAgent).getResult();return n={browser:r?.browser,device:r?.device,os:r?.os,engine:r?.engine},sessionStorage.setItem(e,JSON.stringify(n)),n}catch{return{}}},pt=()=>{const e=[["echo",()=>lt.version],["react",e=>e.React&&e.React.version]].filter((([,e])=>e(window))).reduce(((e,[t,n])=>({...e,[t]:n(window)})),{}),t={format:window?.ECHO_CONFIG?.metadata?.format,region:window?.ECHO_CONFIG?.metadata?.region,country:window?.ECHO_CONFIG?.metadata?.country};return{...e,...ft(),...t}},mt=/https?:\/\/(?:www|epp|store|store-int|storeint|secure+.*).apple.com.*?\//;function gt(e){const t=function(e){if(!e)return{};const t=new URL(e);return t.protocol.startsWith("http")||(t.pathname="pathname"),t}(e),n=function(e){let t,n=e;try{t=decodeURIComponent(e)}catch(n){t=e}if(t.includes("/storepickup"))n="/storepickup";else if(t.includes("/store"))n=`/store${t.split("/store")[1]}`;else if(t.includes("/search"))n="/search";else if(t.includes("/giftcard"))n="/giftcard";else if(t.includes("/shop/bag/saved_bag"))n="/shop/bag/saved_bag";else if(t.includes("/shop/bag"))n="/shop/bag";else if(t.includes("/shop/pdpAddToBag"))n="/shop/pdpAddToBag";else if(t.includes("/shop/order")){const e="/shop/order";n=`${e}${t.split(e)[1].replace(/\d/g,"0").replace(/[\w.-]+@[\w.-]+\.\w+/g,"<EMAIL>")}`}else t.includes("/shop/recap")?n="/shop/recap":t.includes("/shop/start")?n="/shop/start":t.includes("/shop/confirm")?n="/shop/confirm":t.includes("/shop/posThankYou")?n="/shop/posThankYou":t.includes("/shop/yoursaves")?n="/shop/yoursaves":t.includes("/shop")&&(n=`/shop${t.split("/shop")[1]}`);return n.includes("%")&&(n=n.split("%")[0]),n}(t.pathname);return t.hash="",t.href=`${t.protocol}//${t.host}${n}`,t.password="",t.pathname=n,t.search="",t}const vt=gt(window.location);function wt(e){let t=e;t.includes("/shop/product")&&(t="/shop/product");const n=t.split("/");return n.length<=5?t:n.slice(0,4).join("/")}const bt=({metadata:e}={})=>{const t={asDc:dt("as_dc"),cookies:document.cookie.split("; ").map((e=>e.substr(0,e.indexOf("=")))),pageId:window.s&&window.s.pageName||"",host:vt.host,pageHostname:vt.hostname,pagePathname:vt.pathname,pageUrl:vt.href,pageShopPath:wt(vt.pathname),pixelRatio:Math.round(100*window.devicePixelRatio)/100||0,pxro:dt("pxro")||"null",screenHeight:window.screen.height||0,screenWidth:window.screen.width||0,validPageUrl:mt.test(window.location.href),windowInnerHeight:window.innerHeight||0,windowInnerWidth:window.innerWidth||0,windowOrientation:window.screen.orientation?window.screen.orientation.angle:"orientation"in window&&window.orientation||0,windowOuterHeight:window.outerHeight||0,windowOuterWidth:window.outerWidth||0,...pt(),...e},n=document.referrer.includes("apple.com")?gt(document.referrer).href:document.referrer;return t.referer=n,t.referrer=n,t};window.ECHO_CONFIG&&function({config:e,metadata:t}){lt({config:{...e,bundleDecoration:bt({metadata:t})}})}(window.ECHO_CONFIG)})(),Echo={}.default})();