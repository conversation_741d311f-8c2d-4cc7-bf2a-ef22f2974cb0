(()=>{"use strict";var e={d:(t,o)=>{for(var r in o)e.o(o,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:o[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{default:()=>m});var o={};e.r(o),e.d(o,{fetchJSON:()=>S,fetchWithTimeout:()=>b});var r={};e.r(r),e.d(r,{scrollToElement:()=>O,scrollToPosition:()=>$});const s=()=>{let e=!0,t=[];const o=new Map;return{get length(){return o.size},getItem:e=>{const t=o.get(e);return void 0===t?null:t},key:r=>(t=e?Array.from(o.keys()):t,e=!1,r>=0&&r<t.length?t[r]:null),clear:()=>{o.clear(),e=!0},removeItem:t=>{o.delete(t),e=!0},setItem:(t,r)=>{o.set(t,r),e=!0}}};class n{constructor(){this._originUrl=null,this.hash="",this.host="",this.hostname="",this.href="",this.pathname="",this.port="",this.protocol="",this.search=""}get origin(){return this._originUrl?this._originUrl.toString():""}assign(e){try{const t="string"==typeof e?new URL(e):e;this._originUrl=t,this.hash=t.hash,this.host=t.host,this.hostname=t.hostname,this.href=t.href,this.pathname=t.pathname,this.port=t.port,this.protocol=t.protocol,this.search=t.search}catch(e){this._originUrl=null}}replace(e){this.assign(e)}reload(){}}class i extends Array{constructor(){super()}item(e){return this[e]}forEach(){}}class a extends EventTarget{constructor(){super(),this.cookie="",this.referrer="",this.activeElement=null,this.querySelector=()=>null,this.querySelectorAll=()=>new i,this.URL=""}}class c extends EventTarget{constructor(){super(),this.devicePixelRatio=2,this.scrollX=0,this.scrollY=0,this.innerHeight=100,this.innerWidth=100,this.navigator={onLine:!0},this.location=new n,this.document=new a,this.localStorage=s(),this.sessionStorage=s(),this.console=console,this.URL=URL,this.CustomEvent="undefined"==typeof CustomEvent?void 0:CustomEvent,this.atob=atob,this.btoa=btoa,this.performance=performance,this.setTimeout=setTimeout,this.clearTimeout=clearTimeout,this.setInterval=setInterval,this.clearInterval=clearInterval,this.fetch=fetch}getSelection(){return null}}let l="undefined"==typeof window?new c:window;const u=()=>l;l.devicePixelRatio;const h=Object.freeze(["error","warn","info","debug","trace"]),d=Object.freeze({trace:4,debug:3,info:2,warn:1,error:0}),p=e=>"string"==typeof e||"number"==typeof e||"boolean"==typeof e?e:void 0,m=e=>{if(!e)throw new Error("moduleName parameter required for Logger");const t=t=>o=>{const r=(u().sessionStorage.getItem("AS_LOG_LEVEL")||u().AS_LOG_LEVEL||"").toLowerCase(),s=h.find((e=>e===r))||"error",n=d[s];if(d[t]<=n)try{let r;"string"==typeof o||"number"==typeof o||"boolean"==typeof o||"bigint"==typeof o?r={message:o}:Array.isArray(o)?r={message:o.toString()}:(i=o,r={...i,name:"name"in i?p(i.name):void 0,code:"code"in i?p(i.code):void 0,message:"message"in i?p(i.message):void 0,lineno:"lineno"in i?p(i.lineno):void 0,colno:"colno"in i?p(i.colno):void 0,line:"line"in i?p(i.line):void 0,column:"column"in i?p(i.column):void 0,stack:"stack"in i?p(i.stack):"error"in i&&"object"==typeof i.error&&i.error&&"stack"in i.error?p(i.error.stack):void 0});const s=(()=>{const e="undefined"!=typeof window,t="undefined"!=typeof document,o=t&&void 0!==document.createElement;return e&&t&&o?window:null})();(e=>{if("undefined"==typeof CustomEvent){const t=new Event("echoLogEvent");Object.defineProperty(t,"detail",{enumerable:!0,configurable:!0,writable:!0,value:e}),u().dispatchEvent(t)}else{const t=new CustomEvent("echoLogEvent",{detail:e});u().dispatchEvent(t)}})({...r,id:e,type:t.toLowerCase(),currentScriptSrc:s&&s.document&&s.document.currentScript&&"src"in s.document.currentScript&&s.document.currentScript.src}),u().console[t](o)}catch(e){u().console.error("as-utilities/logger: could not log message",e)}var i};return{error:t("error"),warn:t("warn"),info:t("info"),debug:t("debug"),trace:t("trace")}},f=Object.freeze({onFetchSuccess:"onFetchSuccess",onFetchError:"onFetchError"}),g=new EventTarget;class y extends Error{constructor(e,t,o){const r=e.status||0===e.status?e.status:"",s=`${r} ${e.statusText||""}`.trim(),n=s?`response.status: ${s}`:"an unknown error";t.options&&delete t.options.body,t.options&&delete t.options.headers,super(`Request failed with ${n}, response.type: ${e.type}, response.redirected: ${e.redirected}, request.url: ${t.url}, request.options: ${JSON.stringify(t.options)}, content-type: ${e.headers&&e.headers.get("Content-Type")}, x-request-id: ${e.headers&&e.headers.get("x-request-id")}, trace-id: ${o}`),this.name="HTTPError",this.code=r}}class v extends Error{constructor(e,t,o,r){const s=e.status||0===e.status?e.status:"",n=`${s} ${e.statusText||""}`.trim(),i=n?`response.status: ${n}`:"an unknown error",a=o instanceof Error?o.message:"(NONE)";t.options&&delete t.options.body,t.options&&delete t.options.headers,super(`Received non-JSON response from JSON API with error.message: ${a}, ${i}, response.type: ${e.type}, response.redirected: ${e.redirected}, request.url: ${t.url}, request.options: ${JSON.stringify(t.options)}, content-type: ${e.headers&&e.headers.get("Content-Type")}, x-request-id: ${e.headers&&e.headers.get("x-request-id")}, trace-id: ${r}`),this.name="JSONError",this.code=s}}class w extends Error{constructor(e,t){e.options&&delete e.options.body,e.options&&delete e.options.headers,super(`Request timed out after ${e.options&&e.options.timeout} ms, request.url: ${e.url}, request.options: ${JSON.stringify(e.options)}, trace-id: ${t}`),this.name="TimeoutError",this.code=408}}const E=()=>Math.random().toString(36).substring(2,12)+"-"+Date.now().toString(36),b=async(e,t={},o=E())=>{let r;t.timeout=t.timeout||1e4;let s=new AbortController;const n=u(),i={url:e,options:{method:"GET",credentials:"same-origin",signal:s.signal,...t,headers:t.headers||{}}};Array.isArray(i.options.headers)?i.options.headers.push(["x-aos-ui-fetch-call-1",o]):i.options.headers instanceof Headers?i.options.headers.set("x-aos-ui-fetch-call-1",o):i.options.headers["x-aos-ui-fetch-call-1"]=o;const a=n.fetch(i.url,i.options).then((e=>e)),c=new Promise(((e,n)=>{r=setTimeout((()=>{s.abort(),n(new w(i,o))}),t.timeout)}));try{const e=n.performance,t=e?e.now():null,r=await Promise.race([a,c]),s="/shop/541";if(r&&541===r.status&&i.url!==s){try{n.sessionStorage.clear()}catch(e){console.error(e)}try{await S(s)}catch(e){console.error(e)}return n.location.href="/shop/go/404",null}if(r&&r.ok){const s=e?e.now():null;return e&&function({request:e,response:t,start:o,end:r,traceId:s}){const n=m("as-utilities/measureFetch");try{const n=(i=e.url,a=u().location.origin,new URL(i,a)),c="echoPerformanceNowEvent",l={id:"fetch-timer",meta:{hostname:n.hostname,pathname:n.pathname,duration:null!==r&&null!==o?r-o:NaN,responseStatus:t.status,responseType:t.type,contentType:t.headers&&t.headers.get("Content-Type"),xRequestId:t.headers&&t.headers.get("x-request-id"),traceId:s}};if("undefined"==typeof CustomEvent){const e=new Event(c);Object.defineProperty(e,"detail",{enumerable:!0,configurable:!0,writable:!0,value:l}),u().dispatchEvent(e)}else u().dispatchEvent(new CustomEvent(c,{detail:l}))}catch(e){n.error(e)}var i,a}({request:i,response:r,start:t,end:s,traceId:o}),r}if(r)throw new y(r,i,o)}catch(e){throw n.CustomEvent&&g.dispatchEvent(new n.CustomEvent(f.onFetchError,{detail:{error:e}})),e}finally{clearTimeout(r)}},S=async(e,t={})=>{let o;const r=E(),s=u(),n={url:e,options:t};try{o=await b(n.url,n.options,r)}catch(e){throw e}try{const e=await o.json();return s.CustomEvent&&g.dispatchEvent(new s.CustomEvent(f.onFetchSuccess,{detail:e})),e}catch(e){throw new v(o,n,e,r)}},T=(Object.freeze(["linear","ease-in","ease-out","ease-in-out","ease-in-cubic","ease-out-cubic"]),{linear:e=>e,"ease-in":e=>e**2,"ease-out":e=>e*(2-e),"ease-in-out":e=>e<.5?2*e**2:(4-2*e)*e-1,"ease-in-cubic":e=>e**3,"ease-out-cubic":e=>(e-1)**3+1}),$=(e,t,o={})=>new Promise((r=>{const{duration:s=400,easing:n="linear",abortAfterTimeout:i=!0}=o,a=T[n]||T.linear,c=u().scrollY,l=Math.min(Math.max(0,t),e.scrollHeight-u().innerHeight);let h,d=!1;const p=t=>{h||(h=t);const o=a((t-h)/s),n=Math.abs(c-l),i=Math.sign(l-c),u=i*o*n,m=(i>0?Math.min:Math.max)(c+u,l);e.scrollTop=m,(i>0?m>=l:m<=l)||d?r("done"):requestAnimationFrame(p)};c!==l?(requestAnimationFrame(p),i&&u().setTimeout((()=>{d=!0}),s)):r("done")})),O=(e,t,o)=>{const r=t.offsetTop;return $(e,r,o)},{fetchJSON:q,fetchWithTimeout:x}=o,{scrollToPosition:L,scrollToElement:C}=r,{default:P}=t;window.Log=P})();