PS C:\Users\<USER>\Desktop\New folder> & C:/Users/<USER>/AppData/Local/Programs/Python/Python313/python.exe "c:/Users/<USER>/Desktop/New folder/checkout_reproducer_Mac.py"
✅ 已从 credentials.json 成功加载 Apple ID 凭据
2025-08-21 13:26:52,517 - INFO - --- 开始第 1/1 次尝试 ---
2025-08-21 13:26:52,517 - INFO - 目标产品: iPhone 16 Pro Max 黑色钛金属 512GB
2025-08-21 13:26:52,517 - INFO - 产品页面: https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A
2025-08-21 13:26:52,517 - INFO - 启动伪装浏览器以获取动态 atbtoken...
2025-08-21 13:26:52,517 - INFO - ✅ Chrome配置文件目录已存在: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 13:26:52,517 - INFO - ✅ 检测到已有登录数据 (Login Data: 40960 bytes, Preferences: 18873 bytes)
2025-08-21 13:26:52,517 - INFO - ✅ 检测到已有登录状态，将尝试跳过2FA验证
2025-08-21 13:26:52,518 - INFO - 使用headless模式获取atbtoken（无需登录）
2025-08-21 13:26:52,723 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-08-21 13:26:53,321 - INFO - ✅ 浏览器启动成功 (第1次尝试)
2025-08-21 13:26:53,321 - INFO - 检查持久化配置文件的登录状态...
2025-08-21 13:26:55,830 - INFO - 🔍 登录状态检查: URL=https://www.apple.com.cn/shop/bag, 已登录=True
2025-08-21 13:26:55,830 - INFO - ✅ 持久化配置文件登录状态有效，成功跳过2FA验证！
2025-08-21 13:26:55,834 - WARNING - ⚠️ 虽然看起来已登录，但无法获取as_atb cookie，继续正常流程
2025-08-21 13:26:55,834 - INFO - 直接访问产品页以获取 as_atb cookie (eager模式)...
2025-08-21 13:26:57,409 - INFO - ✅ 产品页面加载成功 (第1次尝试)
2025-08-21 13:26:57,409 - INFO - 开始高频轮询检查 as_atb cookie...
2025-08-21 13:26:57,547 - INFO - ✅ 在产品页的第 0.3 秒成功捕获 as_atb cookie!
2025-08-21 13:26:57,547 - INFO - 成功捕获 as_atb cookie: 1.0|MjAyNS0wOC0yMCAxMDoyNjo1Nw|56f74cfa617e9e61beb0ce1bcd4c608f34492b90
2025-08-21 13:26:57,666 - INFO - 获取到的所有cookies: ['mbox', 'at_check', 'as_atb', 'shld_bt_m', 'as_disa', 's_cc', 'as_pcts', 'shld_bt_ck', 's_vi', 's_fid', 'as_dc', 'dssf', 'as_sfa', 'geo', 'dssid2']
2025-08-21 13:26:57,682 - INFO - ✅ 使用持久化配置文件，登录状态已自动保存
2025-08-21 13:26:57,731 - INFO - 🛒 发现购物车中有 2 件商品，准备清空...
2025-08-21 13:26:59,942 - INFO - 💡 建议手动清空购物车或忽略购物车中的现有商品
2025-08-21 13:26:59,943 - INFO - 正在添加第 1/2 件商品... (期望总数: 3)
2025-08-21 13:26:59,943 - INFO - 步骤1/2: 发送POST请求到 https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A (目标: 3件)
2025-08-21 13:27:00,331 - INFO - ✅ POST请求成功，已跳转到attach页面。
2025-08-21 13:27:00,331 - INFO - 步骤2/2: 发送GET请求到状态API以验证购物车内容...
2025-08-21 13:27:00,368 - INFO - 购物车状态API响应: {'items': 3, 'ttl': 180, 'api': {'flyout': '/%5Bstorefront%5D/shop/bag/flyout', 'addToBag': '/%5Bstorefront%5D/shop/bag/add?product=%5Bpart%5D'}}
2025-08-21 13:27:00,369 - INFO - 🎉 验证成功！API确认购物车中现在有 3 件商品。
2025-08-21 13:27:00,369 - INFO - 正在添加第 2/2 件商品... (期望总数: 4)
2025-08-21 13:27:00,370 - INFO - 步骤1/2: 发送POST请求到 https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A (目标: 4件)
2025-08-21 13:27:00,519 - INFO - ✅ POST请求成功，已跳转到attach页面。
2025-08-21 13:27:00,520 - INFO - 步骤2/2: 发送GET请求到状态API以验证购物车内容...
2025-08-21 13:27:00,549 - INFO - 购物车状态API响应: {'items': 4, 'ttl': 180, 'api': {'flyout': '/%5Bstorefront%5D/shop/bag/flyout', 'addToBag': '/%5Bstorefront%5D/shop/bag/add?product=%5Bpart%5D'}}
2025-08-21 13:27:00,550 - INFO - 🎉 验证成功！API确认购物车中现在有 4 件商品。
2025-08-21 13:27:00,552 - INFO - 🎉 成功添加 2 件商品到购物车！
2025-08-21 13:27:00,552 - INFO - 现在开始结账流程...
2025-08-21 13:27:00,552 - INFO - 在浏览器环境中获取 STK 和 商品ID...
2025-08-21 13:27:00,561 - INFO - 导航到购物车页面以解析STK...
2025-08-21 13:27:01,124 - INFO - 已保存购物车页面源码到 cart_page_debug.html
2025-08-21 13:27:01,124 - INFO - 尝试从 script#init_data 中解析STK...
2025-08-21 13:27:01,130 - INFO - ✅ 从 script#init_data 解析到STK: UqSfSyCon-Z76-YPGTKqPDN9KhHmtYzcrKnuCRmUF40
2025-08-21 13:27:01,143 - INFO - ✅ 从页面源码中提取到UUID形式的商品ID: ['cb7a29b3-1cdf-45d0-87b6-96b09ef3a3f1']
2025-08-21 13:27:01,157 - INFO - 已将 15 个浏览器 cookies 加载到最终结账 session 中。
2025-08-21 13:27:01,158 - INFO - 准备为 1 个商品进行结账...
2025-08-21 13:27:01,260 - INFO - 结账请求状态码: 200
2025-08-21 13:27:01,260 - INFO - 🔄 重新启动浏览器以使用持久化配置文件...
2025-08-21 13:27:01,261 - INFO - ✅ Chrome配置文件目录已存在: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 13:27:01,261 - INFO - ✅ 结账浏览器将使用持久化配置文件: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 13:27:01,261 - INFO - 👁️ 使用有头模式运行结账浏览器，便于观察页面跳转
2025-08-21 13:27:01,790 - INFO - ✅ 结账浏览器启动成功，已配置持久化配置文件
2025-08-21 13:27:01,790 - INFO - 导航到登录页: https://secure9.www.apple.com.cn/shop/signIn?ssi=4AAABmMsYQYUBINUDqPtmIcq7f1TY7dGMW8DkcOhJ0bDccQ9jMtrWjqbGAAAAS2h0dHBzOi8vc2VjdXJlOS53d3cuYXBwbGUuY29tLmNuL3Nob3AvY2hlY2tvdXQvc3RhcnQ_cGx0bj02OUI0MzhEN3x8O01ZVFIzfAACAflB04Vra4Gq7fFx4zaBFEdEPmlOxHjVOQGsB0x2ugi-&up=t
2025-08-21 13:27:03,888 - INFO - 页面标题: 登录 — 安全结账 - Apple (中国大陆)
2025-08-21 13:27:03,889 - INFO - 🔐 检测到登录页面，需要进行登录
2025-08-21 13:27:03,889 - INFO - 🔄 检查持久化配置文件中的登录状态...
2025-08-21 13:27:03,897 - INFO - 当前页面URL: https://secure9.www.apple.com.cn/shop/signIn?ssi=4AAABmMsYQYUBINUDqPtmIcq7f1TY7dGMW8DkcOhJ0bDccQ9jMtrWjqbGAAAAS2h0dHBzOi8vc2VjdXJlOS53d3cuYXBwbGUuY29tLmNuL3Nob3AvY2hlY2tvdXQvc3RhcnQ_cGx0bj02OUI0MzhEN3x8O01ZVFIzfAACAflB04Vra4Gq7fFx4zaBFEdEPmlOxHjVOQGsB0x2ugi-&up=t
2025-08-21 13:27:03,900 - INFO - 当前页面标题: 登录 — 安全结账 - Apple (中国大陆)
2025-08-21 13:27:03,900 - INFO - 🔐 检测到登录页面，需要进行登录
2025-08-21 13:27:03,900 - INFO - 导航到登录页: https://secure9.www.apple.com.cn/shop/signIn?ssi=4AAABmMsYQYUBINUDqPtmIcq7f1TY7dGMW8DkcOhJ0bDccQ9jMtrWjqbGAAAAS2h0dHBzOi8vc2VjdXJlOS53d3cuYXBwbGUuY29tLmNuL3Nob3AvY2hlY2tvdXQvc3RhcnQ_cGx0bj02OUI0MzhEN3x8O01ZVFIzfAACAflB04Vra4Gq7fFx4zaBFEdEPmlOxHjVOQGsB0x2ugi-&up=t
2025-08-21 13:27:04,402 - INFO - 页面标题: 登录 — 安全结账 - Apple (中国大陆)
2025-08-21 13:27:04,402 - INFO - 🔍 检测登录页面类型...
2025-08-21 13:27:12,582 - INFO - ⏳ 主页面无登录表单，等待并切换到登录iframe...
2025-08-21 13:27:12,608 - INFO - ✅ 已切换到iframe
2025-08-21 13:27:12,628 - INFO - ✅ 找到邮箱输入框: #account_name_text_field
2025-08-21 13:27:12,727 - INFO - ✅ 已输入邮箱
2025-08-21 13:27:13,261 - INFO - ✅ 找到继续按钮: #sign-in
2025-08-21 13:27:13,271 - INFO - ✅ 已点击继续按钮
2025-08-21 13:27:14,066 - INFO - ✅ 找到密码输入框: #password_text_field
2025-08-21 13:27:14,141 - INFO - ✅ 已输入密码
2025-08-21 13:27:14,668 - INFO - ✅ 找到登录按钮: #sign-in
2025-08-21 13:27:14,677 - INFO - ✅ 已点击登录按钮
2025-08-21 13:27:14,679 - INFO - 🔍 智能检测登录结果：2FA界面、登录成功或页面跳转...
2025-08-21 13:27:19,277 - INFO - ✅ 智能检测：登录成功，已跳转到结账页面
2025-08-21 13:27:19,277 - INFO - ✅ 登录成功，状态已保存到持久化配置文件
2025-08-21 13:27:19,278 - INFO - 浏览器登录成功，准备重试结账...
2025-08-21 13:27:19,280 - INFO - 当前页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 13:27:19,283 - INFO - 当前页面标题: 订单选项 — 安全结账
2025-08-21 13:27:19,399 - INFO - 已保存登录后的调试文件: post_login_debug_20250821_132719.png, post_login_debug_20250821_132719.html
2025-08-21 13:27:19,400 - INFO - 正在从主浏览器同步最新的会话状态...
2025-08-21 13:27:19,404 - INFO - 会话同步完成，当前共有 20 个 cookies。
2025-08-21 13:27:19,404 - INFO - 正在从主浏览器获取最新的STK和商品ID...
2025-08-21 13:27:19,404 - INFO - 在浏览器环境中获取 STK 和 商品ID...
2025-08-21 13:27:19,407 - INFO - 当前已在结账页面，直接解析STK...
2025-08-21 13:27:19,423 - INFO - 已保存结账页面源码到 checkout_page_debug.html
2025-08-21 13:27:19,423 - INFO - 尝试从 script#init_data 中解析STK...
2025-08-21 13:27:19,431 - INFO - ✅ 从 script#init_data 解析到STK: eMtm5iySXCKZhWq5mGqjwyzKjMw
2025-08-21 13:27:19,449 - INFO - ✅ 从页面源码中提取到UUID形式的商品ID: ['cb7a29b3-1cdf-45d0-87b6-96b09ef3a3f1']
2025-08-21 13:27:19,453 - INFO - 检测到已在结账页面，获取结账页面的STK...
2025-08-21 13:27:19,459 - INFO - ✅ 从结账页面获取到新的STK: eMtm5iySXCKZhWq5mGqjwyzKjMw
2025-08-21 13:27:19,459 - INFO - 已刷新到登录后的 item_ids: ['cb7a29b3-1cdf-45d0-87b6-96b09ef3a3f1']
2025-08-21 13:27:19,474 - INFO - ✅ 已成功进入结账页面，跳过隐私弹窗处理，直接执行后续步骤...
2025-08-21 13:27:19,480 - INFO - 会话同步完成，当前共有 20 个 cookies。
2025-08-21 13:27:19,481 - INFO - 🚀 开始执行结账后续步骤（纯API模式优先）...
2025-08-21 13:27:22,500 - INFO - 当前页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 13:27:22,500 - INFO - ✅ 已成功进入结账页面，开始纯API自动化步骤
2025-08-21 13:27:22,501 - INFO - 📍 步骤1: 处理地址选择（纯API模式）...
2025-08-21 13:27:22,501 - INFO - 🔧 纯API模式：处理地址选择步骤...
2025-08-21 13:27:22,501 - INFO - 📋 提取页面init_data...
2025-08-21 13:27:22,506 - INFO - ✅ 成功从script#init_data解析JSON
2025-08-21 13:27:22,506 - INFO - ✅ 从init_data获取到STK: eMtm5iySXCKZhWq5mGqjwyzKjMw
2025-08-21 13:27:22,516 - INFO - 🔧 从init_data构建fulfillment数据...
2025-08-21 13:27:22,517 - INFO - 🔧 标准化地址格式: 广东 深圳 宝安区
2025-08-21 13:27:22,517 - INFO - ✅ 构建了 7 个fulfillment字段
2025-08-21 13:27:22,517 - INFO - 📍 使用地址: 广东 深圳 宝安区
2025-08-21 13:27:22,517 - INFO - 🚚 使用配送选项: A8
2025-08-21 13:27:22,518 - INFO - 🚀 发送纯API地址选择请求到: https://secure9.www.apple.com.cn/shop/checkoutx/fulfillment
2025-08-21 13:27:22,518 - INFO - 📊 请求数据字段数量: 7
2025-08-21 13:27:22,518 - INFO - 🔍 完整的POST数据:
2025-08-21 13:27:22,518 - INFO -    checkout.fulfillment.fulfillmentOptions.selectFulfillmentLocation = HOME
2025-08-21 13:27:22,518 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.state = 广东
2025-08-21 13:27:22,519 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.city = 深圳
2025-08-21 13:27:22,519 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.district = 宝安区
2025-08-21 13:27:22,519 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.provinceCityDistrict = 广东  深圳 宝安区
2025-08-21 13:27:22,519 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.countryCode = CN
2025-08-21 13:27:22,519 - INFO -    checkout.fulfillment.deliveryTab.delivery.shipmentGroups.shipmentGroup-1.shipmentOptionsGroups.shipmentOptionsGroup-1.shippingOptions.selectShippingOption = A8
2025-08-21 13:27:22,519 - INFO - ✅ 所有期望的字段都存在
2025-08-21 13:27:23,140 - INFO - 📡 纯API地址选择响应状态码: 200
2025-08-21 13:27:23,141 - INFO - 📄 响应内容长度: 70024
2025-08-21 13:27:23,142 - INFO - 🔍 分析API响应内容:
2025-08-21 13:27:23,142 - INFO -    响应头状态: 200
2025-08-21 13:27:23,142 - INFO -    页面标题: 送货详情 — 安全结账
2025-08-21 13:27:23,142 - INFO -    页面URL: /shop/checkoutx?_s=Shipping-init
2025-08-21 13:27:23,143 - INFO - ✅ API响应显示成功跳转到shipping步骤
2025-08-21 13:27:23,143 - INFO - 🔄 刷新浏览器页面以同步API状态...
2025-08-21 13:27:26,642 - INFO - 🔍 刷新后页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 13:27:26,642 - INFO - 🔍 刷新后页面标题: 订单选项 — 安全结账
2025-08-21 13:27:26,642 - WARNING - ⚠️ 浏览器页面未同步，API请求可能实际失败
2025-08-21 13:27:26,642 - WARNING - ⚠️ 纯API地址选择失败，尝试UI交互回退...
2025-08-21 13:27:26,642 - INFO - 🖱️ UI回退模式：处理地址选择步骤...
2025-08-21 13:27:26,643 - INFO - 处理地址选择步骤（改进版：UI交互 + API方式）...
2025-08-21 13:27:27,018 - INFO - ✅ 已保存地址选择前状态: fulfillment_before_20250821_132726.png, fulfillment_before_20250821_132726.html
2025-08-21 13:27:27,018 - INFO - 🖱️ 步骤1: 尝试UI交互，确保默认选项被选中...
2025-08-21 13:27:27,018 - INFO - 🖱️ 尝试点击默认的配送选项...
2025-08-21 13:27:41,195 - WARNING - ⚠️ 未找到可点击的配送选项