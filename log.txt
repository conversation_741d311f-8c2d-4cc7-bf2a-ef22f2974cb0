PS C:\Users\<USER>\Desktop\New folder> & C:/Users/<USER>/AppData/Local/Programs/Python/Python313/python.exe "c:/Users/<USER>/Desktop/New folder/checkout_reproducer_Mac.py"
✅ 已从 credentials.json 成功加载 Apple ID 凭据
2025-08-21 13:38:27,603 - INFO - --- 开始第 1/1 次尝试 ---
2025-08-21 13:38:27,604 - INFO - 目标产品: iPhone 16 Pro Max 黑色钛金属 512GB
2025-08-21 13:38:27,604 - INFO - 产品页面: https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A
2025-08-21 13:38:27,604 - INFO - 启动伪装浏览器以获取动态 atbtoken...
2025-08-21 13:38:27,604 - INFO - ✅ Chrome配置文件目录已存在: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 13:38:27,604 - INFO - ✅ 检测到已有登录数据 (Login Data: 40960 bytes, Preferences: 20643 bytes)
2025-08-21 13:38:27,604 - INFO - ✅ 检测到已有登录状态，将尝试跳过2FA验证
2025-08-21 13:38:27,604 - INFO - 使用headless模式获取atbtoken（无需登录）
2025-08-21 13:38:27,807 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-08-21 13:38:28,443 - INFO - ✅ 浏览器启动成功 (第1次尝试)
2025-08-21 13:38:28,443 - INFO - 检查持久化配置文件的登录状态...
2025-08-21 13:38:31,098 - INFO - 🔍 登录状态检查: URL=https://www.apple.com.cn/shop/bag, 已登录=True
2025-08-21 13:38:31,098 - INFO - ✅ 持久化配置文件登录状态有效，成功跳过2FA验证！
2025-08-21 13:38:31,102 - WARNING - ⚠️ 虽然看起来已登录，但无法获取as_atb cookie，继续正常流程
2025-08-21 13:38:31,102 - INFO - 直接访问产品页以获取 as_atb cookie (eager模式)...
2025-08-21 13:38:33,050 - INFO - ✅ 产品页面加载成功 (第1次尝试)
2025-08-21 13:38:33,050 - INFO - 开始高频轮询检查 as_atb cookie...
2025-08-21 13:38:33,273 - INFO - ✅ 在产品页的第 0.3 秒成功捕获 as_atb cookie!
2025-08-21 13:38:33,273 - INFO - 成功捕获 as_atb cookie: 1.0|MjAyNS0wOC0yMCAxMDozODozMg|90bc8967ab31f1b924243bada16ada77ff9be2d6
2025-08-21 13:38:33,340 - INFO - 获取到的所有cookies: ['mbox', 'shld_bt_m', 'at_check', 'as_atb', 'dssf', 'as_sfa', 'as_ltn_cn', 's_cc', 'as_disa', 'as_pcts', 'shld_bt_ck', 's_vi', 's_fid', 'as_dc', 'geo', 'dssid2']
2025-08-21 13:38:33,344 - INFO - ✅ 使用持久化配置文件，登录状态已自动保存
2025-08-21 13:38:33,384 - INFO - 🛒 发现购物车中有 4 件商品，准备清空...
2025-08-21 13:38:35,613 - INFO - 💡 建议手动清空购物车或忽略购物车中的现有商品
2025-08-21 13:38:35,613 - INFO - 正在添加第 1/2 件商品... (期望总数: 5)
2025-08-21 13:38:35,613 - INFO - 步骤1/2: 发送POST请求到 https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A (目标: 5件)
2025-08-21 13:38:37,921 - INFO - ✅ POST请求成功，已跳转到attach页面。
2025-08-21 13:38:37,921 - INFO - 步骤2/2: 发送GET请求到状态API以验证购物车内容...
2025-08-21 13:38:37,955 - INFO - 购物车状态API响应: {'items': 5, 'ttl': 180, 'api': {'flyout': '/%5Bstorefront%5D/shop/bag/flyout', 'addToBag': '/%5Bstorefront%5D/shop/bag/add?product=%5Bpart%5D'}}
2025-08-21 13:38:37,955 - INFO - 🎉 验证成功！API确认购物车中现在有 5 件商品。
2025-08-21 13:38:37,956 - INFO - 正在添加第 2/2 件商品... (期望总数: 6)
2025-08-21 13:38:37,956 - INFO - 步骤1/2: 发送POST请求到 https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A (目标: 6件)
2025-08-21 13:38:38,077 - INFO - ✅ POST请求成功，已跳转到attach页面。
2025-08-21 13:38:38,077 - INFO - 步骤2/2: 发送GET请求到状态API以验证购物车内容...
2025-08-21 13:38:38,115 - INFO - 购物车状态API响应: {'items': 6, 'ttl': 180, 'api': {'flyout': '/%5Bstorefront%5D/shop/bag/flyout', 'addToBag': '/%5Bstorefront%5D/shop/bag/add?product=%5Bpart%5D'}}
2025-08-21 13:38:38,117 - INFO - 🎉 验证成功！API确认购物车中现在有 6 件商品。
2025-08-21 13:38:38,117 - INFO - 🎉 成功添加 2 件商品到购物车！
2025-08-21 13:38:38,118 - INFO - 现在开始结账流程...
2025-08-21 13:38:38,119 - INFO - 在浏览器环境中获取 STK 和 商品ID...
2025-08-21 13:38:38,129 - INFO - 导航到购物车页面以解析STK...
2025-08-21 13:38:38,873 - INFO - 已保存购物车页面源码到 cart_page_debug.html
2025-08-21 13:38:38,874 - INFO - 尝试从 script#init_data 中解析STK...
2025-08-21 13:38:38,880 - INFO - ✅ 从 script#init_data 解析到STK: UqSfSyCon-Z76-YPGTKqPDN9KhHmtYzcrKnuCRmUF40
2025-08-21 13:38:38,893 - INFO - ✅ 从页面源码中提取到UUID形式的商品ID: ['cb7a29b3-1cdf-45d0-87b6-96b09ef3a3f1']
2025-08-21 13:38:38,913 - INFO - 已将 16 个浏览器 cookies 加载到最终结账 session 中。
2025-08-21 13:38:38,914 - INFO - 准备为 1 个商品进行结账...
2025-08-21 13:38:39,014 - INFO - 结账请求状态码: 200
2025-08-21 13:38:39,015 - INFO - 🔄 重新启动浏览器以使用持久化配置文件...
2025-08-21 13:38:39,016 - INFO - ✅ Chrome配置文件目录已存在: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 13:38:39,018 - INFO - ✅ 结账浏览器将使用持久化配置文件: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 13:38:39,020 - INFO - 👁️ 使用有头模式运行结账浏览器，便于观察页面跳转
2025-08-21 13:38:39,599 - INFO - ✅ 结账浏览器启动成功，已配置持久化配置文件
2025-08-21 13:38:39,599 - INFO - 导航到登录页: https://secure9.www.apple.com.cn/shop/checkout/start?pltn=69B438D7
2025-08-21 13:38:43,364 - INFO - 页面标题: 订单选项 — 安全结账
2025-08-21 13:38:43,364 - INFO - ✅ 检测到已在结账页面，说明登录状态有效，直接执行后续步骤
2025-08-21 13:38:43,364 - INFO - 浏览器登录成功，准备重试结账...
2025-08-21 13:38:43,367 - INFO - 当前页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 13:38:43,369 - INFO - 当前页面标题: 订单选项 — 安全结账
2025-08-21 13:38:43,731 - INFO - 已保存登录后的调试文件: post_login_debug_20250821_133843.png, post_login_debug_20250821_133843.html
2025-08-21 13:38:43,732 - INFO - 正在从主浏览器同步最新的会话状态...
2025-08-21 13:38:43,736 - INFO - 会话同步完成，当前共有 15 个 cookies。
2025-08-21 13:38:43,736 - INFO - 正在从主浏览器获取最新的STK和商品ID...
2025-08-21 13:38:43,736 - INFO - 在浏览器环境中获取 STK 和 商品ID...
2025-08-21 13:38:43,739 - INFO - 当前已在结账页面，直接解析STK...
2025-08-21 13:38:43,759 - INFO - 已保存结账页面源码到 checkout_page_debug.html
2025-08-21 13:38:43,759 - INFO - 尝试从 script#init_data 中解析STK...
2025-08-21 13:38:43,776 - INFO - ✅ 从 script#init_data 解析到STK: PX890mbLqMlttN0JuhvT9swZaB4
2025-08-21 13:38:43,800 - INFO - ✅ 从页面源码中提取到UUID形式的商品ID: ['cb7a29b3-1cdf-45d0-87b6-96b09ef3a3f1']
2025-08-21 13:38:43,815 - INFO - 检测到已在结账页面，获取结账页面的STK...
2025-08-21 13:38:43,821 - INFO - ✅ 从结账页面获取到新的STK: PX890mbLqMlttN0JuhvT9swZaB4
2025-08-21 13:38:43,821 - INFO - 已刷新到登录后的 item_ids: ['cb7a29b3-1cdf-45d0-87b6-96b09ef3a3f1']
2025-08-21 13:38:43,841 - INFO - ✅ 已成功进入结账页面，跳过隐私弹窗处理，直接执行后续步骤...
2025-08-21 13:38:43,846 - INFO - 会话同步完成，当前共有 15 个 cookies。
2025-08-21 13:38:43,846 - INFO - 🚀 开始执行结账后续步骤（纯API模式优先）...
2025-08-21 13:38:46,868 - INFO - 当前页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 13:38:46,868 - INFO - ✅ 已成功进入结账页面，开始纯API自动化步骤
2025-08-21 13:38:46,868 - INFO - 📍 步骤1: 处理地址选择（纯API模式）...
2025-08-21 13:38:46,868 - INFO - 🔧 纯API模式：处理地址选择步骤...
2025-08-21 13:38:46,869 - INFO - 📋 提取页面init_data...
2025-08-21 13:38:46,873 - INFO - ✅ 成功从script#init_data解析JSON
2025-08-21 13:38:46,873 - INFO - ✅ 从init_data获取到STK: PX890mbLqMlttN0JuhvT9swZaB4
2025-08-21 13:38:46,881 - INFO - 🔧 从init_data构建fulfillment数据...
2025-08-21 13:38:46,881 - INFO - 📍 使用用户已有地址: 广东 深圳 宝安区
2025-08-21 13:38:46,882 - INFO - 🔧 最终地址格式: 广东 深圳 宝安区
2025-08-21 13:38:46,882 - INFO - ✅ 构建了 7 个fulfillment字段
2025-08-21 13:38:46,882 - INFO - 📍 使用地址: 广东 深圳 宝安区
2025-08-21 13:38:46,882 - INFO - 🚚 使用配送选项: A8
2025-08-21 13:38:46,882 - INFO - 🚀 发送纯API地址选择请求到: https://secure9.www.apple.com.cn/shop/checkoutx/fulfillment
2025-08-21 13:38:46,882 - INFO - 📊 请求数据字段数量: 7
2025-08-21 13:38:46,883 - INFO - 🔍 完整的POST数据:
2025-08-21 13:38:46,883 - INFO -    checkout.fulfillment.fulfillmentOptions.selectFulfillmentLocation = HOME
2025-08-21 13:38:46,883 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.state = 广东
2025-08-21 13:38:46,883 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.city = 深圳
2025-08-21 13:38:46,883 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.district = 宝安区
2025-08-21 13:38:46,883 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.provinceCityDistrict = 广东  深圳 宝安区
2025-08-21 13:38:46,884 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.countryCode = CN
2025-08-21 13:38:46,884 - INFO -    checkout.fulfillment.deliveryTab.delivery.shipmentGroups.shipmentGroup-1.shipmentOptionsGroups.shipmentOptionsGroup-1.shippingOptions.selectShippingOption = A8
2025-08-21 13:38:46,884 - INFO - ✅ 所有期望的字段都存在
2025-08-21 13:38:47,523 - INFO - 📡 纯API地址选择响应状态码: 200
2025-08-21 13:38:47,523 - INFO - 📄 响应内容长度: 69995
2025-08-21 13:38:47,525 - INFO - 🔍 分析API响应内容:
2025-08-21 13:38:47,525 - INFO -    响应头状态: 200
2025-08-21 13:38:47,525 - INFO -    页面标题: 送货详情 — 安全结账
2025-08-21 13:38:47,525 - INFO -    页面URL: /shop/checkoutx?_s=Shipping-init
2025-08-21 13:38:47,525 - INFO - ✅ API响应显示成功跳转到shipping步骤
2025-08-21 13:38:47,525 - INFO - 🔄 刷新浏览器页面以同步API状态...
2025-08-21 13:38:50,684 - INFO - 🔍 刷新后页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 13:38:50,684 - INFO - 🔍 刷新后页面标题: 订单选项 — 安全结账
2025-08-21 13:38:50,684 - WARNING - ⚠️ 浏览器页面未同步，API请求可能实际失败
2025-08-21 13:38:50,684 - INFO - 🖱️ 尝试点击'继续填写送货地址'按钮...
2025-08-21 13:38:50,728 - INFO - ✅ 成功点击继续按钮
2025-08-21 13:38:53,734 - INFO - 🔍 点击后页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Shipping-init
2025-08-21 13:38:53,735 - INFO - 🔍 点击后页面标题: 送货详情 — 安全结账
2025-08-21 13:38:53,735 - INFO - ✅ 点击按钮后成功跳转到shipping步骤
2025-08-21 13:38:53,735 - INFO - 🚚 步骤2: 处理配送方式（纯API模式）...
2025-08-21 13:38:53,735 - INFO - 🔧 纯API模式：处理配送方式步骤...
2025-08-21 13:38:53,735 - INFO - 📋 提取页面init_data...
2025-08-21 13:38:53,740 - INFO - ✅ 成功从script#init_data解析JSON
2025-08-21 13:38:53,747 - INFO - 🔧 从init_data构建shipping数据...
2025-08-21 13:38:53,747 - INFO - ✅ 构建了 1 个shipping字段
2025-08-21 13:38:53,748 - INFO - 🚀 发送纯API配送方式请求到: https://secure9.www.apple.com.cn/shop/checkoutx/shipping
2025-08-21 13:38:54,108 - INFO - 📡 纯API配送方式响应状态码: 200
2025-08-21 13:38:57,135 - ERROR - ❌ 页面显示错误信息
2025-08-21 13:38:57,135 - WARNING - ⚠️ 纯API配送方式可能失败
2025-08-21 13:38:57,135 - WARNING - ⚠️ 纯API配送方式失败，尝试UI交互回退...
2025-08-21 13:38:57,135 - INFO - 🖱️ UI回退模式：处理配送方式步骤...
2025-08-21 13:38:57,135 - INFO - 处理配送方式步骤...
2025-08-21 13:38:57,140 - INFO - ✅ 从页面获取到最新STK: PX890mbLqMlttN0JuhvT9swZaB4
2025-08-21 13:38:57,148 - INFO - 发送配送方式API请求到: https://secure9.www.apple.com.cn/shop/checkoutx/shipping
2025-08-21 13:38:57,273 - INFO - 配送方式API响应状态码: 200
2025-08-21 13:38:57,274 - INFO - 等待页面更新...
2025-08-21 13:39:00,278 - INFO - 配送API调用后页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Shipping-init
2025-08-21 13:39:00,278 - INFO - ✅ 配送方式步骤完成
2025-08-21 13:39:00,278 - INFO - 💳 步骤3: 处理支付方式（纯API模式）...
2025-08-21 13:39:00,278 - INFO - 🔧 纯API模式：处理支付方式步骤...
2025-08-21 13:39:00,278 - INFO - 📋 提取页面init_data...
2025-08-21 13:39:00,283 - INFO - ✅ 成功从script#init_data解析JSON
2025-08-21 13:39:00,290 - INFO - 🔧 从init_data构建billing数据...
2025-08-21 13:39:00,291 - INFO - ✅ 构建了 1 个billing字段
2025-08-21 13:39:00,291 - INFO - 🚀 发送纯API支付方式请求到: https://secure9.www.apple.com.cn/shop/checkoutx/billing
2025-08-21 13:39:00,434 - INFO - 📡 纯API支付方式响应状态码: 200
2025-08-21 13:39:05,482 - INFO - ✅ 页面状态正常
2025-08-21 13:39:05,482 - INFO - ✅ 纯API支付方式成功
2025-08-21 13:39:05,482 - INFO - 🎉 所有结账步骤完成！
2025-08-21 13:39:05,482 - INFO - 🔍 开始验证结账完成状态...
2025-08-21 13:39:08,507 - INFO - 最终页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Shipping-init
2025-08-21 13:39:08,507 - INFO - 最终页面标题: 送货详情 — 安全结账
2025-08-21 13:39:09,045 - INFO - ✅ 已保存最终状态文件: final_checkout_state_20250821_133908.png, final_checkout_state_20250821_133908.html
2025-08-21 13:39:09,087 - INFO - 📊 结账状态验证结果:
2025-08-21 13:39:09,087 - INFO -   ✅ URL仍在checkout流程中
2025-08-21 13:39:09,089 - INFO -   ✅ 页面包含关键内容: 支付方式, 账单地址, 订单摘要
2025-08-21 13:39:09,089 - WARNING -   ⚠️ 页面可能包含错误信息