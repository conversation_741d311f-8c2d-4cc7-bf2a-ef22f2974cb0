PS C:\Users\<USER>\Desktop\New folder> & C:/Users/<USER>/AppData/Local/Programs/Python/Python313/python.exe "c:/Users/<USER>/Desktop/New folder/checkout_reproducer_Mac.py"
✅ 已从 credentials.json 成功加载 Apple ID 凭据
2025-08-21 12:49:56,104 - INFO - --- 开始第 1/1 次尝试 ---
2025-08-21 12:49:56,104 - INFO - 目标产品: iPhone 16 Pro Max 黑色钛金属 512GB
2025-08-21 12:49:56,104 - INFO - 产品页面: https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A
2025-08-21 12:49:56,104 - INFO - 启动伪装浏览器以获取动态 atbtoken...
2025-08-21 12:49:56,104 - INFO - ✅ Chrome配置文件目录已存在: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 12:49:56,105 - INFO - ✅ 检测到已有登录数据 (Login Data: 40960 bytes, Preferences: 18755 bytes)
2025-08-21 12:49:56,105 - INFO - ✅ 检测到已有登录状态，将尝试跳过2FA验证
2025-08-21 12:49:56,105 - INFO - 使用headless模式获取atbtoken（无需登录）
2025-08-21 12:49:56,293 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-08-21 12:49:56,891 - INFO - ✅ 浏览器启动成功 (第1次尝试)
2025-08-21 12:49:56,892 - INFO - 检查持久化配置文件的登录状态...
2025-08-21 12:49:59,346 - INFO - 🔍 登录状态检查: URL=https://www.apple.com.cn/shop/bag, 已登录=True
2025-08-21 12:49:59,346 - INFO - ✅ 持久化配置文件登录状态有效，成功跳过2FA验证！
2025-08-21 12:49:59,350 - WARNING - ⚠️ 虽然看起来已登录，但无法获取as_atb cookie，继续正常流程
2025-08-21 12:49:59,350 - INFO - 直接访问产品页以获取 as_atb cookie (eager模式)...
2025-08-21 12:50:00,995 - INFO - ✅ 产品页面加载成功 (第1次尝试)
2025-08-21 12:50:00,995 - INFO - 开始高频轮询检查 as_atb cookie...
2025-08-21 12:50:01,135 - INFO - ✅ 在产品页的第 0.3 秒成功捕获 as_atb cookie!
2025-08-21 12:50:01,135 - INFO - 成功捕获 as_atb cookie: 1.0|MjAyNS0wOC0yMCAwOTo1MDowMA|3ec6e8bed81b225c29c8bb729f1510029bb2b177
2025-08-21 12:50:01,246 - INFO - 获取到的所有cookies: ['mbox', 'at_check', 'as_atb', 'shld_bt_m', 'dssf', 'as_sfa', 'as_ltn_cn', 's_cc', 'as_disa', 'as_pcts', 'shld_bt_ck', 's_vi', 's_fid', 'as_dc', 'geo', 'dssid2']
2025-08-21 12:50:01,302 - INFO - ✅ 使用持久化配置文件，登录状态已自动保存
2025-08-21 12:50:01,349 - INFO - 🛒 发现购物车中有 39 件商品，准备清空...
2025-08-21 12:50:03,603 - INFO - 💡 建议手动清空购物车或忽略购物车中的现有商品
2025-08-21 12:50:03,603 - INFO - 正在添加第 1/2 件商品... (期望总数: 40)
2025-08-21 12:50:03,603 - INFO - 步骤1/2: 发送POST请求到 https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A (目标: 40件)
2025-08-21 12:50:07,459 - INFO - ✅ POST请求成功，已跳转到attach页面。
2025-08-21 12:50:07,459 - INFO - 步骤2/2: 发送GET请求到状态API以验证购物车内容...
2025-08-21 12:50:07,492 - INFO - 购物车状态API响应: {'items': 40, 'ttl': 180, 'api': {'flyout': '/%5Bstorefront%5D/shop/bag/flyout', 'addToBag': '/%5Bstorefront%5D/shop/bag/add?product=%5Bpart%5D'}}
2025-08-21 12:50:07,493 - INFO - 🎉 验证成功！API确认购物车中现在有 40 件商品。
2025-08-21 12:50:07,494 - INFO - 正在添加第 2/2 件商品... (期望总数: 41)
2025-08-21 12:50:07,495 - INFO - 步骤1/2: 发送POST请求到 https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A (目标: 41件)
2025-08-21 12:50:07,609 - INFO - ✅ POST请求成功，已跳转到attach页面。
2025-08-21 12:50:07,609 - INFO - 步骤2/2: 发送GET请求到状态API以验证购物车内容...
2025-08-21 12:50:07,637 - INFO - 购物车状态API响应: {'items': 41, 'ttl': 180, 'api': {'flyout': '/%5Bstorefront%5D/shop/bag/flyout', 'addToBag': '/%5Bstorefront%5D/shop/bag/add?product=%5Bpart%5D'}}
2025-08-21 12:50:07,638 - INFO - 🎉 验证成功！API确认购物车中现在有 41 件商品。
2025-08-21 12:50:07,639 - INFO - 🎉 成功添加 2 件商品到购物车！
2025-08-21 12:50:07,639 - INFO - 现在开始结账流程...
2025-08-21 12:50:07,640 - INFO - 在浏览器环境中获取 STK 和 商品ID...
2025-08-21 12:50:07,649 - INFO - 导航到购物车页面以解析STK...
2025-08-21 12:50:08,221 - INFO - 已保存购物车页面源码到 cart_page_debug.html
2025-08-21 12:50:08,221 - INFO - 尝试从 script#init_data 中解析STK...
2025-08-21 12:50:08,227 - INFO - ✅ 从 script#init_data 解析到STK: UqSfSyCon-Z76-YPGTKqPDN9KhHmtYzcrKnuCRmUF40
2025-08-21 12:50:08,246 - INFO - ✅ 从页面源码中提取到UUID形式的商品ID: ['d4965e30-d4c1-40e5-8863-eedf3a58ceb6']
2025-08-21 12:50:08,258 - INFO - 已将 16 个浏览器 cookies 加载到最终结账 session 中。
2025-08-21 12:50:08,259 - INFO - 准备为 1 个商品进行结账...
2025-08-21 12:50:08,370 - INFO - 结账请求状态码: 200
2025-08-21 12:50:08,370 - INFO - 🔄 重新启动浏览器以使用持久化配置文件...
2025-08-21 12:50:08,371 - INFO - ✅ Chrome配置文件目录已存在: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 12:50:08,371 - INFO - ✅ 结账浏览器将使用持久化配置文件: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 12:50:08,945 - INFO - ✅ 结账浏览器启动成功，已配置持久化配置文件
2025-08-21 12:50:08,946 - INFO - 导航到登录页: https://secure9.www.apple.com.cn/shop/checkout/start?pltn=69B438D7
2025-08-21 12:50:12,278 - INFO - 页面标题: 订单选项 — 安全结账
2025-08-21 12:50:12,278 - INFO - ✅ 检测到已在结账页面，说明登录状态有效，直接执行后续步骤
2025-08-21 12:50:12,278 - INFO - 浏览器登录成功，准备重试结账...
2025-08-21 12:50:12,281 - INFO - 当前页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 12:50:12,283 - INFO - 当前页面标题: 订单选项 — 安全结账
2025-08-21 12:50:12,665 - INFO - 已保存登录后的调试文件: post_login_debug_20250821_125012.png, post_login_debug_20250821_125012.html
2025-08-21 12:50:12,665 - INFO - 正在从主浏览器同步最新的会话状态...
2025-08-21 12:50:12,669 - INFO - 会话同步完成，当前共有 15 个 cookies。
2025-08-21 12:50:12,669 - INFO - 正在从主浏览器获取最新的STK和商品ID...
2025-08-21 12:50:12,669 - INFO - 在浏览器环境中获取 STK 和 商品ID...
2025-08-21 12:50:12,672 - INFO - 当前已在结账页面，直接解析STK...
2025-08-21 12:50:12,718 - INFO - 已保存结账页面源码到 checkout_page_debug.html
2025-08-21 12:50:12,719 - INFO - 尝试从 script#init_data 中解析STK...
2025-08-21 12:50:12,727 - INFO - ✅ 从 script#init_data 解析到STK: 1Ap0ivqCtwRW3tVas58bPVdzbMg
2025-08-21 12:50:12,737 - INFO - ✅ 从页面源码中提取到UUID形式的商品ID: ['d4965e30-d4c1-40e5-8863-eedf3a58ceb6']
2025-08-21 12:50:12,741 - INFO - 检测到已在结账页面，获取结账页面的STK...
2025-08-21 12:50:12,746 - INFO - ✅ 从结账页面获取到新的STK: 1Ap0ivqCtwRW3tVas58bPVdzbMg
2025-08-21 12:50:12,746 - INFO - 已刷新到登录后的 item_ids: ['d4965e30-d4c1-40e5-8863-eedf3a58ceb6']
2025-08-21 12:50:12,777 - INFO - ✅ 已成功进入结账页面，跳过隐私弹窗处理，直接执行后续步骤...
2025-08-21 12:50:12,793 - INFO - 会话同步完成，当前共有 15 个 cookies。
2025-08-21 12:50:12,795 - INFO - 🚀 开始执行结账后续步骤（纯API模式优先）...
2025-08-21 12:50:15,832 - INFO - 当前页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 12:50:15,832 - INFO - ✅ 已成功进入结账页面，开始纯API自动化步骤
2025-08-21 12:50:15,832 - INFO - 📍 步骤1: 处理地址选择（纯API模式）...
2025-08-21 12:50:15,833 - INFO - 🔧 纯API模式：处理地址选择步骤...
2025-08-21 12:50:15,833 - INFO - 📋 提取页面init_data...
2025-08-21 12:50:15,837 - INFO - ✅ 成功从script#init_data解析JSON
2025-08-21 12:50:15,837 - INFO - ✅ 从init_data获取到STK: 1Ap0ivqCtwRW3tVas58bPVdzbMg
2025-08-21 12:50:15,855 - INFO - 🔧 从init_data构建fulfillment数据...
2025-08-21 12:50:15,856 - INFO - ✅ 构建了 7 个fulfillment字段
2025-08-21 12:50:15,856 - INFO - 📍 使用地址: 广东 深圳 宝安区
2025-08-21 12:50:15,858 - INFO - 🚚 使用配送选项: A8
2025-08-21 12:50:15,858 - INFO - 🚀 发送纯API地址选择请求到: https://secure9.www.apple.com.cn/shop/checkoutx/fulfillment
2025-08-21 12:50:15,858 - INFO - 📊 请求数据字段数量: 7
2025-08-21 12:50:15,858 - INFO - 🔍 完整的POST数据:
2025-08-21 12:50:15,859 - INFO -    checkout.fulfillment.fulfillmentOptions.selectFulfillmentLocation = HOME
2025-08-21 12:50:15,860 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.state = 广东
2025-08-21 12:50:15,863 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.city = 深圳
2025-08-21 12:50:15,863 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.district = 宝安区
2025-08-21 12:50:15,864 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.provinceCityDistrict = 广东  深圳 宝安区
2025-08-21 12:50:15,865 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.countryCode = CN
2025-08-21 12:50:15,865 - INFO -    checkout.fulfillment.deliveryTab.delivery.shipmentGroups.shipmentGroup-1.shipmentOptionsGroups.shipmentOptionsGroup-1.shippingOptions.selectShippingOption = A8
2025-08-21 12:50:15,866 - INFO - ✅ 所有期望的字段都存在
2025-08-21 12:50:16,446 - INFO - 📡 纯API地址选择响应状态码: 200
2025-08-21 12:50:16,447 - INFO - 📄 响应内容长度: 70029
2025-08-21 12:50:16,447 - INFO - ⏳ 等待页面更新...
2025-08-21 12:50:19,467 - ERROR - ❌ 页面显示错误信息
2025-08-21 12:50:19,467 - WARNING - ⚠️ 纯API地址选择可能失败，需要验证
2025-08-21 12:50:19,468 - WARNING - ⚠️ 纯API地址选择失败，尝试UI交互回退...
2025-08-21 12:50:19,468 - INFO - 🖱️ UI回退模式：处理地址选择步骤...
2025-08-21 12:50:19,468 - INFO - 处理地址选择步骤（改进版：UI交互 + API方式）...
2025-08-21 12:50:19,837 - INFO - ✅ 已保存地址选择前状态: fulfillment_before_20250821_125019.png, fulfillment_before_20250821_125019.html
2025-08-21 12:50:19,838 - INFO - 🖱️ 步骤1: 尝试UI交互，确保默认选项被选中...
2025-08-21 12:50:19,838 - INFO - 🖱️ 尝试点击默认的配送选项...