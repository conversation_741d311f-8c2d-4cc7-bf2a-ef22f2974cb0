PS C:\Users\<USER>\Desktop\New folder> & C:/Users/<USER>/AppData/Local/Programs/Python/Python313/python.exe "c:/Users/<USER>/Desktop/New folder/checkout_reproducer_Mac.py"
✅ 已从 credentials.json 成功加载 Apple ID 凭据
2025-08-21 13:05:02,599 - INFO - --- 开始第 1/1 次尝试 ---
2025-08-21 13:05:02,599 - INFO - 目标产品: iPhone 16 Pro Max 黑色钛金属 512GB
2025-08-21 13:05:02,600 - INFO - 产品页面: https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A
2025-08-21 13:05:02,600 - INFO - 启动伪装浏览器以获取动态 atbtoken...
2025-08-21 13:05:02,600 - INFO - ✅ Chrome配置文件目录已存在: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 13:05:02,600 - INFO - ✅ 检测到已有登录数据 (Login Data: 40960 bytes, Preferences: 21259 bytes)
2025-08-21 13:05:02,600 - INFO - ✅ 检测到已有登录状态，将尝试跳过2FA验证
2025-08-21 13:05:02,600 - INFO - 使用headless模式获取atbtoken（无需登录）
2025-08-21 13:05:02,791 - INFO - patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-08-21 13:05:03,391 - INFO - ✅ 浏览器启动成功 (第1次尝试)
2025-08-21 13:05:03,391 - INFO - 检查持久化配置文件的登录状态...
2025-08-21 13:05:05,774 - INFO - 🔍 登录状态检查: URL=https://www.apple.com.cn/shop/bag, 已登录=True
2025-08-21 13:05:05,774 - INFO - ✅ 持久化配置文件登录状态有效，成功跳过2FA验证！
2025-08-21 13:05:05,778 - WARNING - ⚠️ 虽然看起来已登录，但无法获取as_atb cookie，继续正常流程
2025-08-21 13:05:05,778 - INFO - 直接访问产品页以获取 as_atb cookie (eager模式)...
2025-08-21 13:05:06,207 - INFO - ✅ 产品页面加载成功 (第1次尝试)
2025-08-21 13:05:06,208 - INFO - 开始高频轮询检查 as_atb cookie...
2025-08-21 13:05:06,429 - INFO - ✅ 在产品页的第 0.3 秒成功捕获 as_atb cookie!
2025-08-21 13:05:06,429 - INFO - 成功捕获 as_atb cookie: 1.0|MjAyNS0wOC0yMCAxMDowNTowNQ|733cb60e26f7387813ee139423b14fa23f9e96cb
2025-08-21 13:05:06,480 - INFO - 获取到的所有cookies: ['mbox', 'shld_bt_m', 'at_check', 'as_atb', 'dssf', 'as_sfa', 'as_ltn_cn', 's_cc', 'as_disa', 'as_pcts', 'shld_bt_ck', 's_vi', 's_fid', 'as_dc', 'geo', 'dssid2']
2025-08-21 13:05:06,499 - INFO - ✅ 使用持久化配置文件，登录状态已自动保存
2025-08-21 13:05:06,543 - INFO - ✅ 购物车为空，可以开始添加商品
2025-08-21 13:05:06,544 - INFO - 正在添加第 1/2 件商品... (期望总数: 1)
2025-08-21 13:05:06,544 - INFO - 提取的 atbtoken (hash): 733cb60e26f7387813ee139423b14fa23f9e96cb
2025-08-21 13:05:06,545 - INFO - 步骤1/2: 发送POST请求到 https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A (目标: 1件)
2025-08-21 13:05:06,709 - INFO - ✅ POST请求成功，已跳转到attach页面。
2025-08-21 13:05:06,709 - INFO - 步骤2/2: 发送GET请求到状态API以验证购物车内容...
2025-08-21 13:05:06,745 - INFO - 购物车状态API响应: {'items': 1, 'ttl': 180, 'api': {'flyout': '/%5Bstorefront%5D/shop/bag/flyout', 'addToBag': '/%5Bstorefront%5D/shop/bag/add?product=%5Bpart%5D'}}
2025-08-21 13:05:06,758 - INFO - 🎉 验证成功！API确认购物车中现在有 1 件商品。
2025-08-21 13:05:06,760 - INFO - 正在添加第 2/2 件商品... (期望总数: 2)
2025-08-21 13:05:06,760 - INFO - 步骤1/2: 发送POST请求到 https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTR3CH/A (目标: 2件)
2025-08-21 13:05:06,876 - INFO - ✅ POST请求成功，已跳转到attach页面。
2025-08-21 13:05:06,876 - INFO - 步骤2/2: 发送GET请求到状态API以验证购物车内容...
2025-08-21 13:05:06,903 - INFO - 购物车状态API响应: {'items': 2, 'ttl': 180, 'api': {'flyout': '/%5Bstorefront%5D/shop/bag/flyout', 'addToBag': '/%5Bstorefront%5D/shop/bag/add?product=%5Bpart%5D'}}
2025-08-21 13:05:06,911 - INFO - 🎉 验证成功！API确认购物车中现在有 2 件商品。
2025-08-21 13:05:06,912 - INFO - 🎉 成功添加 2 件商品到购物车！
2025-08-21 13:05:06,914 - INFO - 现在开始结账流程...
2025-08-21 13:05:06,914 - INFO - 在浏览器环境中获取 STK 和 商品ID...
2025-08-21 13:05:06,929 - INFO - 导航到购物车页面以解析STK...
2025-08-21 13:05:07,506 - INFO - 已保存购物车页面源码到 cart_page_debug.html
2025-08-21 13:05:07,507 - INFO - 尝试从 script#init_data 中解析STK...
2025-08-21 13:05:07,513 - INFO - ✅ 从 script#init_data 解析到STK: UqSfSyCon-Z76-YPGTKqPDN9KhHmtYzcrKnuCRmUF40
2025-08-21 13:05:07,531 - INFO - ✅ 从页面源码中提取到UUID形式的商品ID: ['cb7a29b3-1cdf-45d0-87b6-96b09ef3a3f1']
2025-08-21 13:05:07,575 - INFO - 已将 16 个浏览器 cookies 加载到最终结账 session 中。
2025-08-21 13:05:07,575 - INFO - 准备为 1 个商品进行结账...
2025-08-21 13:05:07,684 - INFO - 结账请求状态码: 200
2025-08-21 13:05:07,685 - INFO - 🔄 重新启动浏览器以使用持久化配置文件...
2025-08-21 13:05:07,685 - INFO - ✅ Chrome配置文件目录已存在: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 13:05:07,686 - INFO - ✅ 结账浏览器将使用持久化配置文件: c:\Users\<USER>\Desktop\New folder\chrome_profile_apple
2025-08-21 13:05:07,686 - INFO - 🔇 使用无头模式运行结账浏览器
2025-08-21 13:05:07,696 - WARNING - could not detect version_main.therefore, we are assuming it is chrome 108 or higher
2025-08-21 13:05:08,219 - INFO - ✅ 结账浏览器启动成功，已配置持久化配置文件
2025-08-21 13:05:08,219 - INFO - 导航到登录页: https://secure9.www.apple.com.cn/shop/checkout/start?pltn=69B438D7
2025-08-21 13:05:11,739 - INFO - 页面标题: 订单选项 — 安全结账
2025-08-21 13:05:11,740 - INFO - ✅ 检测到已在结账页面，说明登录状态有效，直接执行后续步骤
2025-08-21 13:05:11,740 - INFO - 浏览器登录成功，准备重试结账...
2025-08-21 13:05:11,742 - INFO - 当前页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 13:05:11,745 - INFO - 当前页面标题: 订单选项 — 安全结账
2025-08-21 13:05:11,894 - INFO - 已保存登录后的调试文件: post_login_debug_20250821_130511.png, post_login_debug_20250821_130511.html
2025-08-21 13:05:11,895 - INFO - 正在从主浏览器同步最新的会话状态...
2025-08-21 13:05:11,899 - INFO - 会话同步完成，当前共有 15 个 cookies。
2025-08-21 13:05:11,899 - INFO - 正在从主浏览器获取最新的STK和商品ID...
2025-08-21 13:05:11,899 - INFO - 在浏览器环境中获取 STK 和 商品ID...
2025-08-21 13:05:11,901 - INFO - 当前已在结账页面，直接解析STK...
2025-08-21 13:05:11,953 - INFO - 已保存结账页面源码到 checkout_page_debug.html
2025-08-21 13:05:11,956 - INFO - 尝试从 script#init_data 中解析STK...
2025-08-21 13:05:11,965 - INFO - ✅ 从 script#init_data 解析到STK: g7C_31nP--PnCRwjBDo4m_AE8w0
2025-08-21 13:05:11,976 - INFO - ✅ 从页面源码中提取到UUID形式的商品ID: ['cb7a29b3-1cdf-45d0-87b6-96b09ef3a3f1']
2025-08-21 13:05:11,979 - INFO - 检测到已在结账页面，获取结账页面的STK...
2025-08-21 13:05:11,984 - INFO - ✅ 从结账页面获取到新的STK: g7C_31nP--PnCRwjBDo4m_AE8w0
2025-08-21 13:05:11,984 - INFO - 已刷新到登录后的 item_ids: ['cb7a29b3-1cdf-45d0-87b6-96b09ef3a3f1']
2025-08-21 13:05:12,026 - INFO - ✅ 已成功进入结账页面，跳过隐私弹窗处理，直接执行后续步骤...
2025-08-21 13:05:12,036 - INFO - 会话同步完成，当前共有 15 个 cookies。
2025-08-21 13:05:12,036 - INFO - 🚀 开始执行结账后续步骤（纯API模式优先）...
2025-08-21 13:05:15,054 - INFO - 当前页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 13:05:15,054 - INFO - ✅ 已成功进入结账页面，开始纯API自动化步骤
2025-08-21 13:05:15,054 - INFO - 📍 步骤1: 处理地址选择（纯API模式）...
2025-08-21 13:05:15,054 - INFO - 🔧 纯API模式：处理地址选择步骤...
2025-08-21 13:05:15,055 - INFO - 📋 提取页面init_data...
2025-08-21 13:05:15,059 - INFO - ✅ 成功从script#init_data解析JSON
2025-08-21 13:05:15,059 - INFO - ✅ 从init_data获取到STK: g7C_31nP--PnCRwjBDo4m_AE8w0
2025-08-21 13:05:15,078 - INFO - 🔧 从init_data构建fulfillment数据...
2025-08-21 13:05:15,079 - INFO - 🔧 标准化地址格式: 广东 深圳 宝安区
2025-08-21 13:05:15,079 - INFO - ✅ 构建了 7 个fulfillment字段
2025-08-21 13:05:15,081 - INFO - 📍 使用地址: 广东 深圳 宝安区
2025-08-21 13:05:15,082 - INFO - 🚚 使用配送选项: A8
2025-08-21 13:05:15,082 - INFO - 🚀 发送纯API地址选择请求到: https://secure9.www.apple.com.cn/shop/checkoutx/fulfillment
2025-08-21 13:05:15,083 - INFO - 📊 请求数据字段数量: 7
2025-08-21 13:05:15,084 - INFO - 🔍 完整的POST数据:
2025-08-21 13:05:15,084 - INFO -    checkout.fulfillment.fulfillmentOptions.selectFulfillmentLocation = HOME
2025-08-21 13:05:15,085 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.state = 广东
2025-08-21 13:05:15,085 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.city = 深圳
2025-08-21 13:05:15,085 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.district = 宝安区
2025-08-21 13:05:15,086 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.provinceCityDistrict = 广东  深圳 宝安区
2025-08-21 13:05:15,087 - INFO -    checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.countryCode = CN
2025-08-21 13:05:15,087 - INFO -    checkout.fulfillment.deliveryTab.delivery.shipmentGroups.shipmentGroup-1.shipmentOptionsGroups.shipmentOptionsGroup-1.shippingOptions.selectShippingOption = A8
2025-08-21 13:05:15,088 - INFO - ✅ 所有期望的字段都存在
2025-08-21 13:05:15,780 - INFO - 📡 纯API地址选择响应状态码: 200
2025-08-21 13:05:15,780 - INFO - 📄 响应内容长度: 70024
2025-08-21 13:05:15,781 - INFO - 🔍 分析API响应内容:
2025-08-21 13:05:15,781 - INFO -    响应头状态: 200
2025-08-21 13:05:15,781 - INFO -    页面标题: 送货详情 — 安全结账
2025-08-21 13:05:15,782 - INFO -    页面URL: /shop/checkoutx?_s=Shipping-init
2025-08-21 13:05:15,782 - INFO - ✅ API响应显示成功跳转到shipping步骤
2025-08-21 13:05:15,782 - INFO - 🚚 步骤2: 处理配送方式（纯API模式）...
2025-08-21 13:05:15,782 - INFO - 🔧 纯API模式：处理配送方式步骤...
2025-08-21 13:05:15,782 - INFO - 📋 提取页面init_data...
2025-08-21 13:05:15,787 - INFO - ✅ 成功从script#init_data解析JSON
2025-08-21 13:05:15,813 - INFO - 🔧 从init_data构建shipping数据...
2025-08-21 13:05:15,813 - INFO - ✅ 构建了 1 个shipping字段
2025-08-21 13:05:15,814 - INFO - 🚀 发送纯API配送方式请求到: https://secure9.www.apple.com.cn/shop/checkoutx/shipping
2025-08-21 13:05:16,163 - INFO - 📡 纯API配送方式响应状态码: 200
2025-08-21 13:05:19,200 - INFO - ✅ 页面状态正常
2025-08-21 13:05:19,200 - INFO - ✅ 纯API配送方式成功
2025-08-21 13:05:19,201 - INFO - 💳 步骤3: 处理支付方式（纯API模式）...
2025-08-21 13:05:19,201 - INFO - 🔧 纯API模式：处理支付方式步骤...
2025-08-21 13:05:19,201 - INFO - 📋 提取页面init_data...
2025-08-21 13:05:19,206 - INFO - ✅ 成功从script#init_data解析JSON
2025-08-21 13:05:19,226 - INFO - 🔧 从init_data构建billing数据...
2025-08-21 13:05:19,226 - INFO - ✅ 构建了 1 个billing字段
2025-08-21 13:05:19,227 - INFO - 🚀 发送纯API支付方式请求到: https://secure9.www.apple.com.cn/shop/checkoutx/billing
2025-08-21 13:05:19,367 - INFO - 📡 纯API支付方式响应状态码: 200
2025-08-21 13:05:24,387 - INFO - ✅ 页面状态正常
2025-08-21 13:05:24,388 - INFO - ✅ 纯API支付方式成功
2025-08-21 13:05:24,388 - INFO - 🎉 所有结账步骤完成！
2025-08-21 13:05:24,388 - INFO - 🔍 开始验证结账完成状态...
2025-08-21 13:05:27,408 - INFO - 最终页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 13:05:27,408 - INFO - 最终页面标题: 订单选项 — 安全结账
2025-08-21 13:05:27,531 - INFO - ✅ 已保存最终状态文件: final_checkout_state_20250821_130527.png, final_checkout_state_20250821_130527.html
2025-08-21 13:05:27,551 - INFO - 📊 结账状态验证结果:
2025-08-21 13:05:27,551 - INFO -   ✅ URL仍在checkout流程中
2025-08-21 13:05:27,551 - INFO -   ✅ 页面包含关键内容: 支付方式, 账单地址, 订单摘要
2025-08-21 13:05:27,551 - WARNING -   ⚠️ 页面可能包含错误信息
2025-08-21 13:05:35,769 - WARNING - ⚠️ 验证结果: 结账状态不明确，请检查页面
2025-08-21 13:05:35,769 - WARNING - ⚠️ 结账状态验证未通过，但API步骤已完成
2025-08-21 13:05:35,769 - INFO - 🎉 结账API步骤完成！
2025-08-21 13:05:35,770 - INFO - ✅ 纯API模式结账完成，所有步骤都已成功处理
2025-08-21 13:05:35,770 - INFO - 📋 结账摘要:
2025-08-21 13:05:35,770 - INFO -   ✅ 地址选择 - 完成
2025-08-21 13:05:35,770 - INFO -   ✅ 配送方式 - 完成
2025-08-21 13:05:35,770 - INFO -   ✅ 支付方式 - 完成
2025-08-21 13:05:35,770 - INFO - 🔒 浏览器将自动关闭
2025-08-21 13:05:35,771 - INFO - 🔒 浏览器已关闭
2025-08-21 13:05:35,771 - INFO - 🎉 结账成功！正在跳转到支付页面...

>>> 🎉 最终状态: 成功！请在浏览器中检查结果。
>>> 💡 浏览器将保持打开状态，请手动确认购买完成情况。
2025-08-21 13:05:35,772 - INFO - 🌐 浏览器保持打开状态，请手动检查结果
2025-08-21 13:05:35,772 - INFO - --- 苹果商店自动购买流程结束 ---
PS C:\Users\<USER>\Desktop\New folder> 