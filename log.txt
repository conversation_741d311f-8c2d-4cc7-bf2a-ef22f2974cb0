2025-08-21 12:44:25,613 - INFO - 当前页面标题: 订单选项 — 安全结账
2025-08-21 12:44:25,992 - INFO - 已保存登录后的调试文件: post_login_debug_20250821_124425.png, post_login_debug_20250821_124425.html
2025-08-21 12:44:25,993 - INFO - 正在从主浏览器同步最新的会话状态...
2025-08-21 12:44:25,997 - INFO - 会话同步完成，当前共有 15 个 cookies。
2025-08-21 12:44:25,997 - INFO - 正在从主浏览器获取最新的STK和商品ID...
2025-08-21 12:44:25,997 - INFO - 在浏览器环境中获取 STK 和 商品ID...
2025-08-21 12:44:26,000 - INFO - 当前已在结账页面，直接解析STK...
2025-08-21 12:44:26,061 - INFO - 已保存结账页面源码到 checkout_page_debug.html
2025-08-21 12:44:26,061 - INFO - 尝试从 script#init_data 中解析STK...
2025-08-21 12:44:26,070 - INFO - ✅ 从 script#init_data 解析到STK: jbnI7TR0ErBHzM20LTJxFO3tYf0
2025-08-21 12:44:26,091 - INFO - ✅ 从页面源码中提取到UUID形式的商品ID: ['d4965e30-d4c1-40e5-8863-eedf3a58ceb6']
2025-08-21 12:44:26,101 - INFO - 检测到已在结账页面，获取结账页面的STK...
2025-08-21 12:44:26,156 - INFO - ✅ 从结账页面获取到新的STK: jbnI7TR0ErBHzM20LTJxFO3tYf0
2025-08-21 12:44:26,158 - INFO - 已刷新到登录后的 item_ids: ['d4965e30-d4c1-40e5-8863-eedf3a58ceb6']
2025-08-21 12:44:26,184 - INFO - ✅ 已成功进入结账页面，跳过隐私弹窗处理，直接执行后续步骤...
2025-08-21 12:44:26,190 - INFO - 会话同步完成，当前共有 15 个 cookies。
2025-08-21 12:44:26,190 - INFO - 🚀 开始执行结账后续步骤（纯API模式优先）...
2025-08-21 12:44:29,211 - INFO - 当前页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 12:44:29,211 - INFO - ✅ 已成功进入结账页面，开始纯API自动化步骤
2025-08-21 12:44:29,211 - INFO - 📍 步骤1: 处理地址选择（纯API模式）...
2025-08-21 12:44:29,211 - INFO - 🔧 纯API模式：处理地址选择步骤...
2025-08-21 12:44:29,211 - INFO - 📋 提取页面init_data...
2025-08-21 12:44:29,216 - INFO - ✅ 成功从script#init_data解析JSON
2025-08-21 12:44:29,216 - INFO - ✅ 从init_data获取到STK: jbnI7TR0ErBHzM20LTJxFO3tYf0
2025-08-21 12:44:29,235 - INFO - 🔧 从init_data构建fulfillment数据...
2025-08-21 12:44:29,237 - INFO - ✅ 构建了 7 个fulfillment字段
2025-08-21 12:44:29,237 - INFO - 📍 使用地址: 广东 深圳 宝安区
2025-08-21 12:44:29,238 - INFO - 🚚 使用配送选项: A8
2025-08-21 12:44:29,238 - INFO - 🚀 发送纯API地址选择请求到: https://secure9.www.apple.com.cn/shop/checkoutx/fulfillment
2025-08-21 12:44:29,238 - INFO - 📊 请求数据字段数量: 7
2025-08-21 12:44:29,807 - INFO - 📡 纯API地址选择响应状态码: 200
2025-08-21 12:44:29,807 - INFO - 📄 响应内容长度: 70029
2025-08-21 12:44:29,807 - INFO - ⏳ 等待页面更新...
2025-08-21 12:44:32,833 - ERROR - ❌ 页面显示错误信息
2025-08-21 12:44:32,833 - WARNING - ⚠️ 纯API地址选择可能失败，需要验证
2025-08-21 12:44:32,833 - WARNING - ⚠️ 纯API地址选择失败，尝试UI交互回退...
2025-08-21 12:44:32,833 - INFO - 🖱️ UI回退模式：处理地址选择步骤...
2025-08-21 12:44:32,833 - INFO - 处理地址选择步骤（改进版：UI交互 + API方式）...
2025-08-21 12:44:33,190 - INFO - ✅ 已保存地址选择前状态: fulfillment_before_20250821_124432.png, fulfillment_before_20250821_124432.html
2025-08-21 12:44:33,190 - INFO - 🖱️ 步骤1: 尝试UI交互，确保默认选项被选中...
2025-08-21 12:44:33,190 - INFO - 🖱️ 尝试点击默认的配送选项...
2025-08-21 12:44:47,384 - WARNING - ⚠️ 未找到可点击的配送选项
2025-08-21 12:44:59,666 - WARNING - ⚠️ 未找到可点击的继续按钮
2025-08-21 12:44:59,666 - WARNING - ⚠️ UI交互失败，继续使用API方式
2025-08-21 12:44:59,671 - INFO - ✅ 从页面获取到最新STK: jbnI7TR0ErBHzM20LTJxFO3tYf0
2025-08-21 12:44:59,671 - INFO - ✅ 从页面获取到最新STK: jbnI7TR0ErBHzM20LTJxFO3tYf0
2025-08-21 12:44:59,671 - INFO - 📋 步骤3: 获取表单数据...
2025-08-21 12:44:59,672 - INFO - 📋 尝试获取页面表单数据...
2025-08-21 12:45:00,676 - INFO - 方法1获取到 1 个表单字段
2025-08-21 12:45:00,677 - INFO - 从页面获取的fulfillment表单数据: {'deliveryOptions-checkout.fulfillment.deliveryTab.delivery.shipmentGroups.shipmentGroup-1.shipmentOptionsGroups.shipmentOptionsGroup-1.shippingOptions': 'A8'}
2025-08-21 12:45:00,687 - INFO - 🔧 构建完整的fulfillment数据...
2025-08-21 12:45:00,687 - INFO - 使用页面获取的 1 个字段
2025-08-21 12:45:00,730 - INFO - 提取到的地址信息: {'state': '广东', 'city': '深圳', 'district': '宝安区'}
2025-08-21 12:45:00,730 - INFO - 📋 提取页面init_data...
2025-08-21 12:45:00,743 - INFO - ✅ 成功从script#init_data解析JSON
2025-08-21 12:45:00,743 - INFO - ✅ 从init_data更新了地址信息
2025-08-21 12:45:00,744 - INFO - 最终构建了 8 个字段的fulfillment数据
2025-08-21 12:45:00,744 - INFO - 🔑 checkout.fulfillment.fulfillmentOptions.selectFulfillmentLocation: HOME
2025-08-21 12:45:00,744 - INFO - 🔑 checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.provinceCityDistrict: 广东 深圳 宝安区
2025-08-21 12:45:00,744 - INFO - 🔑 checkout.fulfillment.deliveryTab.delivery.shipmentGroups.shipmentGroup-1.shipmentOptionsGroups.shipmentOptionsGroup-1.shippingOptions.selectShippingOption: A8
2025-08-21 12:45:00,744 - INFO - 发送地址选择API请求到: https://secure9.www.apple.com.cn/shop/checkoutx/fulfillment
2025-08-21 12:45:00,744 - INFO - 请求数据字段数量: 8
2025-08-21 12:45:00,745 - INFO - 关键请求数据: {'checkout.fulfillment.deliveryTab.delivery.shipmentGroups.shipmentGroup-1.shipmentOptionsGroups.shipmentOptionsGroup-1.shippingOptions': 'A8', 'checkout.fulfillment.fulfillmentOptions.selectFulfillmentLocation': 'HOME', 'checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.state': '广东', 'checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.city': '深圳', 'checkout.fulfillment.deliveryTab.delivery.deliveryLocation.address.provinceCityDistrictTabsForCheckout.district': '宝安区'}
2025-08-21 12:45:00,891 - INFO - 地址选择API响应状态码: 200
2025-08-21 12:45:00,891 - INFO - 响应内容长度: 69800
2025-08-21 12:45:00,892 - INFO - 等待页面更新...
2025-08-21 12:45:03,895 - INFO - API调用后页面URL: https://secure9.www.apple.com.cn/shop/checkout?_s=Fulfillment-init
2025-08-21 12:45:03,896 - WARNING - ⚠️ 页面仍在地址选择步骤，可能需要更多信息
2025-08-21 12:45:03,918 - ERROR - ❌ 页面显示错误信息，地址选择失败
2025-08-21 12:45:03,919 - ERROR - ❌ 地址选择步骤完全失败（API + UI都失败）
2025-08-21 12:45:03,919 - ERROR - ❌ 结账步骤执行失败
2025-08-21 12:45:03,920 - ERROR - 结账失败，将尝试重试...
2025-08-21 12:45:03,920 - ERROR - 所有 1 次尝试都失败了