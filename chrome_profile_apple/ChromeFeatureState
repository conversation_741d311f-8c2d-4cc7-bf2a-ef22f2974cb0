{"disable-features": "AutoPictureInPictureForVideoPlayback<AutoPictureInPictureForVideoPlayback,AutofillEnableCardBenefitsForBmo<AutofillEnableCardBenefitsForBmo,AutofillPaymentsFieldSwapping<AutofillPaymentsFieldSwapping,AvoidCloneArgsOnExtensionFunctionDispatch<ReducePPMs,AvoidUnnecessaryGetMinimizeButtonOffset<ReducePPMs,AvoidUnnecessaryShouldRenderRichAnimation<ReducePPMs,BeaconLeakageLogging<BeaconLeakageLogging,ChromeWebStoreNavigationThrottle<ChromeWebStoreNavigationThrottle,CompositorLoadingAnimations<CompositorLoadingAnimations,DelayLayerTreeViewDeletionOnLocalSwap<RenderDocumentWithNavigationQueueing,DocumentPolicyExpectNoLinkedResources<PreloadInlineDeferredImages,FledgeConsiderKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,FledgeEnforceKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,FledgeQueryKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,GetCookiesOnSet<FasterSetCookie,MediaRecorderSeekableWebm<MediaRecorderSeekableWebmKillSwitch,OmniboxDocumentProviderEnterpriseEligibilityWhenUnknown<OmniboxDriveEligibility,OptimizeHTMLElementUrls<PreloadInlineDeferredImages,PaintHolding,PartitionAllocFewerMemoryRegions<PartitionAllocFewerMemoryRegionsMac,PermissionsPromptSurvey<CameraMicPreview,PrefetchScheduler<PrefetchScheduler,PreloadLinkRelDataUrls<PreloadInlineDeferredImages,PreloadTopChromeWebUILessNavigations<PreloadTopChromeWebUILessNavigations,PreloadingNoSamePageFragmentAnchorTracking<PreloadingNoSamePageFragmentAnchorTracking,PrivacySandboxNoticeQueue<PrivacySandboxNoticeQueue,ProgrammaticScrollAnimationOverride<ProgrammaticScrollAnimationOverride,ReadingListEnableSyncTransportModeUponSignIn<UnoDesktopBookmarksAndReadingList,ReducePPMs<ReducePPMs,RenderDocumentCompositorReuse<RenderDocumentWithNavigationQueueing,ScreenWinDisplayLookupByHMONITOR<ReducePPMs,SkipModerateMemoryPressureLevelMac<SkipModerateMemoryPressureLevelMac,SyncEnableBookmarksInTransportMode<UnoDesktopBookmarksAndReadingList,TabCaptureInfobarLinks<TabCaptureInfobarLinks,UpdateDirectManipulationHelperOnParentChange<ReducePPMs,V8Flag_ignition_elide_redundant_tdz_checks<V8IgnitionElideRedundantTdzChecksKillSwitch,V8Flag_late_heap_limit_check<V8LateHeapLimitCheck,VizDisplayCompositor,WebUIJSErrorReportingExtended<DialDownWebUIJSErrorReportingExtended", "enable-features": "ANGLEPerContextBlobCache<ANGLEPerContextBlobCache,*AccessibilityBlockFlowIterator<AXBlockFlowIterator,*AccessibilityPerformanceMeasurementExperiment<AccessibilityPerformanceMeasurementExperiment,*AllowD3D11WarpFallback<SwiftShaderDeprecation,*AllowSoftwareGLFallbackDueToCrashes<SwiftShaderDeprecation,*AllowSwiftShaderFallback<SwiftShaderDeprecation,AppleKeychainUseSecItem<MacKeychainApiMigration,AsyncSetCookie<FasterSetCookie,AutofillDetectFieldVisibility<AutofillDetectFieldVisibility,AutofillEnableCardBenefitsIph<AutofillEnableCardBenefitsIph,*AutofillEnableEmailOrLoyaltyCardsFilling<AutofillEnableLoyaltyCardsFilling,*AutofillEnableFlatRateCardBenefitsFromCurinos<AutofillEnableFlatRateCardBenefitsFromCurinos,*AutofillEnableLoyaltyCardsFilling<AutofillEnableLoyaltyCardsFilling,AutofillFixSplitCreditCardImport<AutofillFixSplitCreditCardImport,AutofillGreekRegexes<AutofillGreekRegexes,*AutofillImproveCityFieldClassification<AutofillImproveCityFieldClassification,AutofillOptimizeFormExtraction<AutofillOptimizeFormExtraction,AutofillPopupZOrderSecuritySurface<AutofillPopupZOrderSecuritySurface_V2,*AutofillPreferSavedFormAsSubmittedForm<AutofillImproveSubmissionDetectionV2,*AutofillRequireCvcForPossibleCardUpdate<AutofillRequireCvcForPossibleCardUpdate,*AutofillServerUploadMoreData<AutofillServerUploadMoreData,AutofillUnifyRationalizationAndSectioningOrder<AutofillUnifyRationalizationAndSectioningOrder,*AutofillUseINAddressModel<AutofillI18nINAddressModel,*AutofillUseSubmittedFormInHtmlSubmission<AutofillImproveSubmissionDetectionV2,*AvoidDuplicateDelayBeginFrame<AvoidDuplicateDelayBeginFrame,BackForwardCachePrioritizedEntry<BackForwardCachePrioritizedEntry,CameraMicPreview<CameraMicPreview,Canvas2DHibernation<CanvasHibernationExperiments,Canvas2DHibernationReleaseTransferMemory<CanvasHibernationExperiments,CanvasHibernationSnapshotZstd<CanvasHibernationExperiments,ClearCanvasResourcesInBackground<CanvasHibernationExperiments,*ClientSideDetectionLlamaForcedTriggerInfoForScamDetection<ClientSideDetectionLlamaForcedTriggerInfoForScamDetection,*ClientSideDetectionOnlyExtractVisualFeatures<ClientSideDetectionOnlyExtractVisualFeatures,ComposeV3Migration<ComposeV3Migration,*CompositeBGColorAnimation<CompositeBackgroundColorAnimation,*DbdRevampDesktop<DbdRevampDesktop,DefaultSiteInstanceGroups<DefaultSiteInstanceGroups,*DeferImplInvalidation<CompositeBackgroundColorAnimation,DeprecateUnload<DeprecateUnload,DeprecateUnloadByAllowList<DeprecateUnload,DirectCompositorThreadIpc<DirectCompositorThreadIpcMacLinuxChromeOS,*DwaFeature<DwaFeature,EnableExtensionsExplicitBrowserSignin<EnableExtensionsExplicitBrowserSignin,*EnableFingerprintingProtectionFilter<FingerprintingProtectionFilter,*EnableHangWatcher<EnableHangWatcher,EnableICloudKeychainRecoveryFactor<MacICloudKeychainRecoveryFactor,EnablePolicyPromotionBanner<EnablePolicyPromotionBanner,*EnableSinglePageAppDataProtection<EnableSinglePageAppDataProtection,EnableTLS13EarlyData<EnableTLS13EarlyData,*EncryptedPrefHashing<EncryptedPrefHashing,EnhancedFieldsForSecOps<EnhancedFieldsForSecOps,EnhancedSecurityEventFields<EnhancedFieldsForSecOps,EnterpriseFileSystemAccessDeepScan<EnterpriseFileSystemAccessDeepScan,*EsbAsASyncedSetting<EsbAsASyncedSetting,EvictionUnlocksResources<CanvasHibernationExperiments,ExpiredHistogramLogic<ExpiredHistograms,*ExtendedReportingRemovePrefDependency<ExtendedReportingRemovePrefDependency,ExtensionManifestV2Unsupported<ExtensionManifestV2Deprecation,*ExtremeLightweightUAFDetector<ExtremeLightweightUAFDetector,*FastPathNoRaster<FastPathNoRaster,*FledgeAuctionDealSupport<ProtectedAudienceDealsSupport,FledgeCacheKAnonHashedKeys<ProtectedAudienceKAnonymityKeyCacheStudy,FledgeFacilitatedTestingSignalsHeaders<FledgeFacilitatedTestingSignalsHeaders,*FledgeTrustedSignalsKVv1CreativeScanning<ProtectedAudienceTrustedSignalsKVv1CreativeScanning,FreezingOnBatterySaver<FreezingOnBatterySaver,FurtherOptimizeParsingDataUrls<SimdutfBase64Support,GetUserMediaDeferredDeviceSettingsSelection<CameraMicPreview,GlicClosedCaptioning<GlicClosedCaptioning,*IPH_AutofillAiOptIn<AutofillAiIPHString,IPH_AutofillCreditCardBenefit<AutofillEnableCardBenefitsIph,IncreaseCookieAccessCacheSize<IncreaseCookieAccessCacheSize,LensOverlayBackToPage<LensOverlayBackToLivePage,LensSearchSidePanelNewFeedback<ChromnientNewFeedback,LensSearchSidePanelScrollToAPI<LensSearchSidePanelScrollToAPI,LocalIpAddressInEvents<LocalIpAddressInEvents,LocalWebApprovals<LocalWebApprovalsLinuxMacWindows,*MachPortRendezvousValidatePeerRequirements<UseAdHocSigningForWebAppShims,*MemoryCacheChangeStrongReferencePruneDelay<MemoryCacheStrongRefPruningTuneUp,*MemoryCacheStrongReference<MemoryCacheStrongRefPruningTuneUp,MemoryPurgeOnFreezeLimit<MemoryPurgeOnFreezeLimit,NotificationTelemetry<NotificationTelemetryService,NtpFooter<DesktopNewTabPage,OmniboxDocumentProvider<OmniboxDriveEligibility,OmniboxDocumentProviderEnterpriseEligibility<OmniboxDriveEligibility,OmniboxDocumentProviderNoSyncRequirement<OmniboxDriveEligibility,OmniboxDocumentProviderPrimaryAccountRequirement<OmniboxDriveEligibility,OmniboxMiaZPS<OmniboxAiModeZpsLaunch,PassHistogramSharedMemoryOnLaunch<PassHistogramSharedMemoryOnLaunch,PasswordsGrouperHeapIntegrityKillSwitch<IOSPasswordsGrouperHeapIntegrityKillSwitch,PdfInk2<PdfInkSignatures,*PdfOopif<PdfOutOfProcessIframe,PerformanceInterventionNotificationImprovements<PerformanceInterventionAlgorithm,PerformanceInterventionNotificationStringImprovements<PerformanceInterventionString,PinnedTabToastOnClose<PinnedTabToastOnClose,*PreconnectNonSearchOmniboxSuggestions<PreconnectNonSearchOmniboxSuggestions,PrefetchServiceWorker<PrefetchServiceWorker,PrivacySandboxAdTopicsContentParity<PrivacySandboxPrivacyGuideAdTopics,PrivacySandboxAdsApiUxEnhancements<PrivacySandboxAdsApiUxEnhancements,*ProgressiveAccessibility<ProgressiveAccessibility,PwaNavigationCapturing<PWANavigationCapturingV2WindowMacLinux,QueueNavigationsWhileWaitingForCommit<RenderDocumentWithNavigationQueueing,QuicDoesNotUseFeatures<QUIC,*ReadAnythingReadAloud<ReadAnythingReadAloudDesktop,RenderDocument<RenderDocumentWithNavigationQueueing,*SafeBrowsingHashPrefixRealTimeLookupsSamplePing<ExtendedReportingRemovePrefDependency,ScriptStreamingForNonHTTP<WebUIInProcessResourceLoading,*ShoppingAlternateServer<ShoppingAlternateServer,ShowSuggestionsOnAutofocus<ShowSuggestionsOnAutofocus,SidePanelResizing<SidePanelResizing,*SignInPromoMaterialNextUI<SignInPromoMaterialNextUI,SimdutfBase64Support<SimdutfBase64Support,*SkiaGraphite<SkiaGraphite,SoftNavigationDetectionAdvancedPaintAttribution<SoftNavigationDetectionAdvancedPaintAttribution,*SoftNavigationDetectionPrePaintBasedAttribution<SoftNavigationDetectionPrePaintBasedAttribution,*SqlScopedTransactionWebDatabase<SqlScopedTransactionWebDatabase,SupervisedUserBlockInterstitialV3<LocalWebApprovalsLinuxMacWindows,*SyncAutofillLoyaltyCard<AutofillEnableLoyaltyCardsFilling,TabGroupShortcuts<TabGroupShortcuts,TabGroupSyncServiceDesktopMigration<TabGroupSyncServiceDesktopMigrationRelaunch,TaskManagerDesktopRefresh<TaskManagerDesktopRefresh,*TcpConnectionPoolSizeTrial<TcpConnectionPoolSizeTrial,UMAPseudoMetricsEffect<UMA-Pseudo-Metrics-Effect-Injection-25-Percent,Ukm<UKM,UkmSamplingRate<UkmSamplingRate,*UseAdHocSigningForWebAppShims<UseAdHocSigningForWebAppShims,UserRemoteCommands<ProfileRemoteCommands,UserRemoteCommandsInvalidationWithDirectMessagesEnabled<ProfileRemoteCommands,V8Flag_large_page_pool<V8LargePagePool,VSyncAlignedPresent<VSyncAlignedPresent,*WasmTtsComponentUpdaterV3Enabled<WasmTtsComponentUpdaterV3Enabled,*WebGPUEnableRangeAnalysisForRobustness<WebGPUEnableRangeAnalysisForRobustness,WebRtcEncodedTransformDirectCallback<WebRtcEncodedTransformDirectCallback,WebUIInProcessResourceLoading<WebUIInProcessResourceLoading", "force-fieldtrial-params": "BackForwardCachePrioritizedEntry.PrioritizeUnlessShouldClearAllAndNoEviction_20250814:level/prioritize-unless-should-clear-all-and-no-eviction,DeprecateUnload.Enabled_top_5:allowlist/a%2Ecom%2Cb%2Ecom%2Cc%2Ecom%2Cd%2Ecom%2Ce%2Ecom%2Cf%2Ecom%2Cweb-platform%2Etest%2Cwww1%2Eweb-platform%2Etest%2C127%2E0%2E0%2E1%2Cexample%2Etest%2Cwww%2Egoogle%2Ecom%2Cwww%2Eyoutube%2Ecom%2Cwww%2Efacebook%2Ecom%2Cwww%2Epornhub%2Ecom%2Cwww%2Exvideos%2Ecom/rollout_percent/0,ExpiredHistograms.ExpiryEnabledWithAllowlist:allowlist/OptimizationGuide%2EModelExecutor%2EModelLoadingDuration2%2EGeolocationPermissions%2COptimizationGuide%2EModelExecutor%2EModelLoadingDuration2%2EGeolocationPermissionsV3%2COptimizationGuide%2EModelExecutor%2EModelLoadingDuration2%2ENotificationPermissions%2COptimizationGuide%2EModelExecutor%2EModelLoadingDuration2%2ENotificationPermissionsV3%2CNet%2EQuicMultiPort%2ENumMultiPortFailureWhenPathDegrading%2CNet%2EQuicMultiPort%2ENumMultiPortFailureWhenPathNotDegrading,PWANavigationCapturingV2WindowMacLinux.EnabledSettingOnByDefault_Launched:link_capturing_state/reimpl_default_on,PerformanceInterventionAlgorithm.EnabledLessAggressive_Launched:availability/any/event_trigger/name%3Aperformance_intervention_dialog_trigger%3Bcomparator%3A%3C3%3Bwindow%3A1%3Bstorage%3A360/event_used/name%3Aperformance_intervention_dialog_used%3Bcomparator%3Aany%3Bwindow%3A0%3Bstorage%3A360/event_weekly_trigger/name%3Aperformance_intervention_dialog_trigger%3Bcomparator%3A%3C21%3Bwindow%3A7%3Bstorage%3A360/minimum_time_reshow/1h/no_acceptance_back_off/7d/scale_max_times_per_day/3/scale_max_times_per_week/21/session_rate/any/session_rate_impact/none/window_size/10,PerformanceInterventionString.EnabledString3_20250807:string_version/3,QUIC.TcpBbrStableAug2025:channel/F/enable_quic/false/epoch/30000000,RenderDocumentWithNavigationQueueing.Default_20250311:level/subframe/queueing_level/full,UKM.Enabled_20180314:WhitelistEntries/AboutThisSiteStatus%2CAbusiveExperienceHeuristic%2CAbusiveExperienceHeuristic%2EJavaScriptDialog%2CAbusiveExperienceHeuristic%2ETabUnder%2CAbusiveExperienceHeuristic%2EWindowOpen%2CAccessibility%2EImageDescriptions%2CAccessibility%2ERenderer%2CAccuracyTipDialog%2CAccuracyTipStatus%2CAdFrameLoad%2CAdPageLoad%2CAdsIntervention%2ELastIntervention%2CAmpPageLoad%2CAndroid%2EDarkTheme%2EAutoDarkMode%2CAndroid%2EMultiWindowChangeActivity%2CAndroid%2EMultiWindowState%2CAndroid%2EScreenRotation%2CAndroid%2EUserRequestedUserAgentChange%2CAppListAppClickData%2CAppListAppLaunch%2CAppListNonAppImpression%2CAutofill%2ECardUploadDecision%2CAutofill%2EDeveloperEngagement%2CAutofill%2EEditedAutofilledFieldAtSubmission%2CAutofill%2EFieldFillStatus%2CAutofill%2EFieldTypeValidation%2CAutofill%2EFormEvent%2CAutofill%2EFormFillSuccessIOS%2CAutofill%2EFormSubmitted%2CAutofill%2EHiddenRepresentationalFieldSkipDecision%2CAutofill%2EInteractedWithForm%2CAutofill%2ERepeatedServerTypePredictionRationalized%2CAutofill%2ESelectedMaskedServerCard%2CAutofill%2ESuggestionFilled%2CAutofill%2ESuggestionsShown%2CAutofill%2ETextFieldDidChange%2CAutofillAssistant%2EInChromeTriggering%2CAutofillAssistant%2ELiteScriptFinished%2CAutofillAssistant%2ELiteScriptOnboarding%2CAutofillAssistant%2ELiteScriptShownToUser%2CAutofillAssistant%2ELiteScriptStarted%2CAutofillAssistant%2ETiming%2CBackForwardCacheDisabledForRenderFrameHostReason%2CBackForwardCacheDisallowActivationReason%2CBackgroundFetch%2CBackgroundFetchDeletingRegistration%2CBackgroundSyncCompleted%2CBackgroundSyncRegistered%2CBadging%2CBlink%2EContextMenu%2EImageSelection%2CBlink%2EFindInPage%2CBlink%2EHTMLParsing%2CBlink%2EPageLoad%2CBlink%2EPaintTiming%2CBlink%2EScript%2EAsyncScripts%2CBlink%2EUpdateTime%2CBlink%2EUseCounter%2CBloatedRenderer%2CChromeOSApp%2EInstalledApp%2CChromeOSApp%2ELaunch%2CChromeOSApp%2EUninstallApp%2CChromeOSApp%2EUsageTime%2CClickInput%2CClientRenderingAPI%2CCompositor%2ERendering%2CCompositor%2EUserInteraction%2CContactsPicker%2EShareStatistics%2CContentIndex%2EAdded%2CContentIndex%2EDeletedByUser%2CContentIndex%2EOpened%2CContextMenuAndroid%2ESelected%2CContextMenuAndroid%2EShown%2CContextualSearch%2CContextualSuggestions%2CCPUUsageMeasurement%2CCrossOriginSubframeWithoutEmbeddingControl%2CDataReductionProxy%2CDetachedWindows%2EExperimental%2CDocument%2EOutliveTimeAfterShutdown%2CDocumentCreated%2CDownload%2ECompleted%2CDownload%2EInterrupted%2CDownload%2EResumed%2CDownload%2EStarted%2CEvent%2EScrollBegin%2ETouch%2CEvent%2EScrollBegin%2EWheel%2CEvent%2EScrollUpdate%2ETouch%2CEvent%2EScrollUpdate%2EWheel%2CExtensions%2ECrossOriginFetchFromContentScript3%2CExtensions%2EWebRequest%2EKeepaliveRequestFinished%2CFileSystemAPI%2EWebRequest%2CFlocPageLoad%2CFontMatchAttempts%2CGeneratedNavigation%2CGoogleDocsOfflineExtension%2CGraphics%2ESmoothness%2EEventLatency%2CGraphics%2ESmoothness%2ELatency%2CGraphics%2ESmoothness%2ENormalizedPercentDroppedFrames%2CGraphics%2ESmoothness%2EPercentDroppedFrames%2CGraphics%2ESmoothness%2EThroughput%2CHistoryClusters%2CHistoryManipulationIntervention%2CHistoryNavigation%2CIdentifiability%2CInputEvent%2CInputMethod%2EAssistive%2EMatch%2CInputMethod%2ENonCompliantApi%2CInstalledRelatedApps%2CIntervention%2EDocumentWrite%2EScriptBlock%2CIOS%2EFindInPageSearchMatches%2CIOS%2EIsDefaultBrowser%2CIOS%2EPageAddedToReadingList%2CIOS%2EPageReadability%2CIOS%2EPageZoomChanged%2CIOS%2ERendererGone%2CIOS%2EURLMismatchInLegacyAndSlimNavigationManager%2CJavascriptFrameworkPageLoad%2CLayout%2EDisplayCutout%2EStateChanged%2CLiteVideo%2CLoadCountsPerTopLevelDocument%2CLoadingPredictor%2CLocalNetworkRequests%2CLoginDetection%2CLookalikeUrl%2ENavigationSuggestion%2CMainFrameDownload%2CMainFrameNavigation%2CMedia%2EAutoplay%2EAttempt%2CMedia%2EAutoplay%2EAudioContext%2CMedia%2EAutoplay%2EMuted%2EUnmuteAction%2CMedia%2EBasicPlayback%2CMedia%2EEME%2EApiPromiseRejection%2CMedia%2EEME%2ECreateMediaKeys%2CMedia%2EEME%2ERequestMediaKeySystemAccess%2CMedia%2EEngagement%2ESessionFinished%2CMedia%2EEngagement%2EShortPlaybackIgnored%2CMedia%2EFeed%2EDiscover%2CMedia%2EGlobalMediaControls%2EActionButtonPressed%2CMedia%2EKaleidoscope%2ENavigation%2CMedia%2ELearning%2EPredictionRecord%2CMedia%2ESiteMuted%2CMedia%2EVideoDecodePerfRecord%2CMedia%2EWatchTime%2CMedia%2EWebAudio%2EAudioContext%2EAudibleTime%2CMedia%2EWebMediaPlayerState%2CMediaRouter%2ECastWebSenderExtensionLoadUrl%2CMediaRouter%2ESiteInitiatedMirroringStarted%2CMediaRouter%2ETabMirroringStarted%2CMemory%2EExperimental%2CMemory%2ETabFootprint%2CMixedContentAutoupgrade%2EResourceRequest%2CMobileFriendliness%2CMobileMenu%2EDirectShare%2CMobileMenu%2EFindInPage%2CMobileMenu%2EShare%2CNavigationPredictorAnchorElementMetrics%2CNavigationPredictorPageLinkClick%2CNavigationPredictorPageLinkMetrics%2CNavigationPredictorRendererWarmup%2CNavigationTiming%2CNet%2ELegacyTLSVersion%2CNoStatePrefetch%2CNotification%2COfflineMeasurements%2COfflinePages%2ESavePageRequested%2COmniboxSecurityIndicator%2EFormSubmission%2COptimizationGuide%2COptimizationGuideAutotuning%2CPageContentAnnotations%2CPageDomainInfo%2CPageForegroundSession%2CPageInfoBubble%2CPageLoad%2CPageLoad%2EFromGoogleSearch%2CPageLoad%2EInternal%2CPageLoad%2EServiceWorkerControlled%2CPageLoad%2ESignedExchange%2CPageLoadCapping%2CPageWithPassword%2CPaintPreviewCapture%2CPasswordForm%2CPasswordManager%2EWellKnownChangePasswordResult%2CPaymentApp%2ECheckoutEvents%2CPaymentRequest%2ECheckoutEvents%2CPaymentRequest%2ETransactionAmount%2CPepper%2EBroker%2CPerfectHeuristics%2CPerformanceAPI%2ELongTask%2CPerformanceAPI%2EMemory%2CPerformanceAPI%2EMemory%2ELegacy%2CPeriodicBackgroundSyncEventCompleted%2CPeriodicBackgroundSyncRegistered%2CPermission%2CPermissionUsage%2CPlugins%2EFlashInstance%2CPopup%2EClosed%2CPopup%2EPage%2CPortal%2EActivate%2CPostMessage%2EIncoming%2EFrame%2CPostMessage%2EIncoming%2EPage%2CPowerUsageScenariosIntervalData%2CPrefetchProxy%2CPrefetchProxy%2EAfterSRPClick%2CPrefetchProxy%2EPrefetchedResource%2CPrerenderPageLoad%2CPreviews%2CPreviewsCoinFlip%2CPreviewsDeferAllScript%2CPreviewsResourceLoadingHints%2CPublicImageCompressionDataUse%2CPublicImageCompressionImageLoad%2CPWA%2EVisit%2CReaderModeActivated%2CReaderModeReceivedDistillability%2CRendererSchedulerTask%2CRenderViewContextMenu%2EUsed%2CResponsiveness%2EUserInteraction%2CResponsivenessMeasurement%2CSameSiteDifferentSchemeRequest%2CSameSiteDifferentSchemeResponse%2CSchemefulSameSiteContextDowngrade%2CScreenBrightness%2CSecurity%2ESafetyTip%2CSecurity%2ESiteEngagement%2CSharedHighlights%2ELinkGenerated%2CSharedHighlights%2ELinkOpened%2CSharing%2EClickToCall%2CShopping%2EChromeCart%2CShopping%2EFormSubmitted%2CShopping%2EWillSendRequest%2CSiteIsolation%2EXSD%2EBrowser%2EBlocked%2CSmartCharging%2CSMSReceiver%2CSSL%2EMixedContentShown%2CSSL%2EMixedContentShown2%2CSubframeDownload%2CSubresourceFilter%2CSubresourceRedirect%2EPublicSrcVideoCompression%2CTab%2ERendererOOM%2CTab%2EScreenshot%2CTabManager%2EBackground%2EFirstAlertFired%2CTabManager%2EBackground%2EFirstAudioStarts%2CTabManager%2EBackground%2EFirstFaviconUpdated%2CTabManager%2EBackground%2EFirstNonPersistentNotificationCreated%2CTabManager%2EBackground%2EFirstTitleUpdated%2CTabManager%2EBackground%2EForegroundedOrClosed%2CTabManager%2EExperimental%2EBackgroundTabOpening%2ETabSwitchLoadStopped%2CTabManager%2EExperimental%2ESessionRestore%2EForegroundTab%2EPageLoad%2CTabManager%2EExperimental%2ESessionRestore%2ETabSwitchLoadStopped%2CTabManager%2ELifecycleStateChange%2CTabManager%2ETabLifetime%2CTabManager%2ETabMetrics%2CTabManager%2EWindowMetrics%2CTouchToFill%2EShown%2CTranslate%2CTranslatePageLoad%2CTrustedWebActivity%2ELocationDelegation%2CTrustedWebActivity%2EOpen%2CTrustedWebActivity%2EQualityEnforcementViolation%2CUnload%2CUserActivity%2CUserActivityId%2CUserSettingsEvent%2CV8%2EWasm%2EModuleCompiled%2CV8%2EWasm%2EModuleDecoded%2CV8%2EWasm%2EModuleInstantiated%2CV8%2EWasm%2EModuleTieredUp%2CVirtualKeyboard%2EOpen%2CWebAPK%2EInstall%2CWebAPK%2ESessionEnd%2CWebAPK%2EUninstall%2CWebAPK%2EVisit%2CWebApp%2EDailyInteraction%2CWebOTPImpact%2CWebRTC%2EAddressHarvesting%2CWebRTC%2EComplexSdp%2CWorker%2EClientAdded%2CXR%2EPageSession%2CXR%2EWebXR%2CXR%2EWebXR%2EPresentationSession%2CXR%2EWebXR%2ESession%2CXR%2EWebXR%2ESessionRequest,UMA-Pseudo-Metrics-Effect-Injection-25-Percent.MediumEffect_05_20241105:multiplicative_factor/1%2E01,UkmSamplingRate.Downsampled_202309:AbandonedSRPNavigation/5/AbusiveExperienceHeuristic%2EJavaScriptDialog/20/AdFrameLoad/60/AdPageLoad/14/AdsIntervention%2ELastIntervention/2/Android%2EMultiWindowState/10/Autofill%2EDeveloperEngagement/14/Autofill%2EFieldFillStatus/70/Autofill%2EFieldTypeValidation/220/Autofill%2EFormEvent/16/Autofill%2ETextFieldDidChange/2/BTM%2EShortVisit/8/BTM%2EShortVisitNeighbor/14/Blink%2EFrameLoader/1300/Blink%2EHTMLParsing/35/Blink%2EJavaScriptFramework%2EVersions/3/Blink%2EPageLoad/45/Blink%2EPaintTiming/120/Blink%2EUpdateTime/90/BrowsingTopics%2EDocumentBrowsingTopicsApiResult2/800/BrowsingTopics%2EPageLoad/3/ChargeEventHistory/4/ChromeOSApp%2EInputEvent/ChromeOSApp/ChromeOSApp%2EInstalledApp/ChromeOSApp/ChromeOSApp%2ELaunch/ChromeOSApp/ChromeOSApp%2EUninstallApp/ChromeOSApp/ChromeOSApp%2EUsageTime/ChromeOSApp/ChromeOSApp%2EUsageTimeReusedSourceId/ChromeOSApp/ClientHints%2EAcceptCHFrameUsage/240/ClientHints%2EAcceptCHHeaderUsage/120/ClientHints%2EDelegateCHMetaUsage/6/ClientRenderingAPI/900/Compose%2ETextElementUsage/4/ContentManagementSystemPageLoad/40/Conversions%2ESourceRegistration/8/Conversions%2ETriggerRegistration/4/Cookies%2EBlocked%2EDueToOriginMismatch/180/Cookies%2EFirstPartyPartitionedInCrossSiteContextV3/1000/CrossOriginSubframeWithoutEmbeddingControl/200/DIPS%2ENavigationFlowNode/3/DIPS%2ETrustIndicator%2EDirectNavigationV2/3/DailyChargeSummary/5/DocumentCreated/270/DomDistiller%2EAndroid%2EDistillabilityLatency/10/DomDistiller%2EAndroid%2EDistillabilityResult/14/DomDistiller%2EModelResult%2EDistillable/30/Download%2ECompleted/Download/Download%2EInterrupted/Download/Download%2EResumed/Download/Download%2EStarted/Download/Event%2EScroll/300/Event%2EScrollJank%2EPredictorJank/550/Extensions%2EOnNavigation/160/Extensions%2EWebRequest%2EKeepaliveRequestFinished/240/GeneratedNavigation/4/GoogleDocsOfflineExtension/35/Graphics%2ESmoothness/30/Graphics%2ESmoothness%2EEventLatency/Graphics%2ESmoothness/Graphics%2ESmoothness%2EFrameSequence/Graphics%2ESmoothness/Graphics%2ESmoothness%2ELatency/Graphics%2ESmoothness/Graphics%2ESmoothness%2ENormalizedPercentDroppedFrames/Graphics%2ESmoothness/HistoryApi%2EAdvanceMethod/25/HistoryManipulationIntervention/6/InstalledRelatedApps/14/JavascriptFrameworkPageLoad/40/LoadCountsPerTopLevelDocument/50/LoadingPredictor/25/LocalNetworkRequests/30/MainFrameNavigation%2EZstdContentEncoding/4/Media/80/Media%2EAutoplay%2EAttempt/35/Media%2EAutoplay%2EAudioContext/2/Media%2EBasicPlayback/Media/Media%2EEME%2EGetStatusForPolicy/20/Media%2EEME%2ERequestMediaKeySystemAccess/2/Media%2EVideoDecodePerfRecord/18/Media%2EWebAudio%2EAudioContext%2EAudibleTime/Media/Media%2EWebMediaPlayerState/Media/Memory%2EExperimental/220/Memory%2ETabFootprint/40/MixedContentAutoupgrade%2EResourceRequest/6/MobileFriendliness/4/MobileFriendliness%2ETappedBadTargets/35/Navigation%2EFromGoogleSearch%2EAbandoned/3/Navigation%2EFromGoogleSearch%2ETimingInformation/6/Navigation%2EReceivedResponse/50/NavigationPredictor/90/NavigationPredictorAnchorElementMetrics/NavigationPredictor/NavigationPredictorPageLinkClick/NavigationPredictor/NavigationPredictorPageLinkMetrics/NavigationPredictor/NavigationPredictorPreloadOnHover/NavigationPredictor/NavigationPredictorUserInteractions/NavigationPredictor/NavigationThrottleDeferredTime/10/NavigationTiming/30/Network%2EDataUrls/240/Notification/3/OmniboxSecurityIndicator%2EFormSubmission/6/OpenerHeuristic%2EPostPopupCookieAccess/25/OptimizationGuide/120/OptimizationGuide%2EAnnotatedPageContent/3/PageContentAnnotations2/60/PageDomainInfo/35/PageForegroundSession/60/PageLoadInitiatorForAdTagging/40/PaintPreviewCapture/2/PartitionedCookiePresentV2/8100/PasswordManager%2EFirstCCTPageLoad/2/PerformanceAPI%2ELongAnimationFrame/1100/PerformanceAPI%2ELongTask/50/PerformanceAPI%2EMemory%2ELegacy/1700/PerformanceManager%2EFreezingEligibility/6/PerformanceManager%2EPageResourceUsage2/360/PermissionUsage/16/PermissionUsage%2ENotificationShown/8/Popup/3/Popup%2EClosed/Popup/Popup%2EPage/Popup/PowerUsageScenariosIntervalData/35/PrefetchProxy/35/PrefetchProxy%2EAfterSRPClick/12/PrefetchProxy%2EPrefetchedResource/8/Preloading%2EAttempt/PreloadingAttempt/Preloading%2EAttempt%2EPreviousPrimaryPage/PreloadingAttempt/Preloading%2EPrediction/220/Preloading%2EPrediction%2EPreviousPrimaryPage/270/RedirectHeuristic%2ECookieAccess2/4/RedirectHeuristic%2ECookieAccessThirdParty2/4/Responsiveness%2EUserInteraction/70/SamesiteRedirectContextDowngrade/12/Security%2ESiteEngagement/35/ServiceWorker%2EMainResourceLoadCompleted/18/ServiceWorker%2EOnLoad/4/SharedStorage%2EWorklet%2EOnDestroyed/10/Site%2EInstall/8/Site%2EManifest/8/Site%2EQuality/8/SiteFamiliarityHeuristicResult/45/SiteInstance/2/SoftNavigation/14/SubresourceFilter/45/SubresourceLoad%2EZstdContentEncoding/850/TabManager%2ETabLifetime/10/TabRevisitTracker%2ETabStateChange/40/ThirdPartyCookies%2EBreakageIndicator%2EHTTPError/60/Translate/40/TranslatePageLoad/60/Unload/35/UserPerceivedPageVisit/120/V8%2EWasm/12/V8%2EWasm%2EModuleCompiled/V8%2EWasm/V8%2EWasm%2EModuleDecoded/V8%2EWasm/V8%2EWasm%2EModuleInstantiated/V8%2EWasm/WebOTPImpact/20/WindowProxyUsage/4/XR%2EWebXR/2/_default_sampling/1/_webdx_features_sampling/1,VSyncAlignedPresent.EnabledLaunch:Target/Interaction", "force-fieldtrials": "ANGLEPerContextBlobCache/Enabled_20250723/AXBlockFlowIterator/Default/*AccessibilityPerformanceMeasurementExperiment/Default/*AdsP4V2/Enabled_20250806/AutoPictureInPictureForVideoPlayback/Control_20250813_50/AutofillAiIPHString/Default/AutofillDetectFieldVisibility/Enabled_50/AutofillEnableCardBenefitsForBmo/Control_50/AutofillEnableCardBenefitsIph/EnabledLaunch/AutofillEnableFlatRateCardBenefitsFromCurinos/Default/AutofillEnableLoyaltyCardsFilling/Default/AutofillFixSplitCreditCardImport/EnabledLaunch/AutofillGreekRegexes/EnabledLaunch/AutofillI18nINAddressModel/Default/AutofillImproveCityFieldClassification/Default/AutofillImproveSubmissionDetectionV2/Default/AutofillOptimizeFormExtraction/EnabledLaunch/AutofillPaymentsFieldSwapping/Control_20250801/AutofillPopupZOrderSecuritySurface_V2/EnabledLaunch/AutofillRequireCvcForPossibleCardUpdate/Default/AutofillServerUploadMoreData/Default/AutofillUnifyRationalizationAndSectioningOrder/Enabled_50/AvoidDuplicateDelayBeginFrame/Default/*AvoidUnnecessaryBeforeUnloadCheckSync/Default/*BackForwardCachePrioritizedEntry/PrioritizeUnlessShouldClearAllAndNoEviction_20250814/*BeaconLeakageLogging/Control_50/*BoostRenderProcessForLoading/Default/*CacheSharingForPervasiveScripts/Preperiod68/CameraMicPreview/CameraOther/CanvasHibernationExperiments/Enabled_20250728/*ChromeChannelStable/Enabled/ChromeWebStoreNavigationThrottle/Control_50/*ChromnientFetchSrp/FetchSrpEnabled_20250325/*ChromnientNewFeedback/Launched/*ChromnientPermissionBubbleAlt/Default_v2/ClientSideDetectionLlamaForcedTriggerInfoForScamDetection/Default/ClientSideDetectionOnlyExtractVisualFeatures/Default/*ComposeV3Migration/EnabledLaunch/CompositeBackgroundColorAnimation/Default/CompositorLoadingAnimations/Control_50/DbdRevampDesktop/Default/DefaultSiteInstanceGroups/EnabledLaunch/*DeferSpeculativeRFHCreation/Preperiod14_20250806/*DeprecateUnload/Enabled_top_5/*DesktopNewTabPage/Preperiod63_PP_20250812/*DesktopNtpModuleSignInRequirement/Default/*DeviceBoundSessionAccessObserverSharedRemote/Preperiod10_20250815/DialDownWebUIJSErrorReportingExtended/Default/DirectCompositorThreadIpcMacLinuxChromeOS/EnabledLaunch/*DisableProxyForSameSitePrefetches/Default/*DwaFeature/Default/EnableExtensionsExplicitBrowserSignin/EnabledLaunch/*EnableHangWatcher/Default/EnablePolicyPromotionBanner/EnabledLaunch/EnableSinglePageAppDataProtection/Default/*EnableTLS13EarlyData/Enabled_20250716/EncryptedPrefHashing/Default/EnhancedFieldsForSecOps/EnabledLaunch/EnterpriseFileSystemAccessDeepScan/EnabledLaunch/EsbAsASyncedSetting/Default/*EventTimingIgnorePresentationTimeFromUnexpectedFrameSource/Default/*ExpiredHistograms/ExpiryEnabledWithAllowlist/ExtendedReportingRemovePrefDependency/Default/*ExtensionManifestV2Deprecation/Enabled_HardDeprecation_Launched_Stable/*ExtremeLightweightUAFDetector/Default/FastPathNoRaster/Default/FasterSetCookie/AsyncSetCookieLaunch/*FedCmSegmentationPlatform/Default/*FieldRankServerClassification/Default/FingerprintingProtectionFilter/Default/FledgeFacilitatedTestingSignalsHeaders/EnabledLaunch/*FreezingOnBatterySaver/EnabledLaunch/GlicClosedCaptioning/EnabledLaunchLaunch/*GlicFreWarming/Default/*HttpCacheNoVarySearch/Default/IOSPasswordsGrouperHeapIntegrityKillSwitch/Disabled_EmergencyKillSwitch/IncreaseCookieAccessCacheSize/EnabledLaunch/*KeepDefaultSearchEngineAlive/Default/*LensOverlayBackToLivePage/BackToLivePageEnabled_20250717/*LensSearchSidePanelScrollToAPI/Launched/*LoadingNetworking2025/Default_20250501/*LoadingPhaseBufferTimeAfterFirstMeaningfulPaint/Default/LocalIpAddressInEvents/EnabledLaunch/*LocalWebApprovalsLinuxMacWindows/EnabledLaunch/MacICloudKeychainRecoveryFactor/EnabledLaunch/MacKeychainApiMigration/EnabledLaunch/MediaRecorderSeekableWebmKillSwitch/Disabled_EmergencyKillSwitch/MemoryCacheStrongRefPruningTuneUp/Default/MemoryPurgeOnFreezeLimit/Enabled20250721/*MultipleLayerMemberRefGWSTestStudy/GroupB_20241008/*MultipleLayerMemberRefTestStudy/GroupA_20241008/NotificationTelemetryService/EnabledLaunch/*OmniboxAiModeZpsLaunch/Launch/*OmniboxDriveEligibility/Launched/*OmniboxRestoreInvisibleFocusOnly/Default/*OptimizationTargetClientSidePhishingGradualRollout/Enabled_20250814/*PWANavigationCapturingV2WindowMacLinux/EnabledSettingOnByDefault_Launched/*PartitionAllocFewerMemoryRegionsMac/Control_20250721/*PassHistogramSharedMemoryOnLaunch/EnabledLaunch/*PdfInkSignatures/Default_20250618/PdfOutOfProcessIframe/Default/PerformanceInterventionAlgorithm/EnabledLessAggressive_Launched/PerformanceInterventionString/EnabledString3_20250807/*PermissionSiteSettingsRadioButton/Default/*PermissionsAIv3/Default/PinnedTabToastOnClose/EnabledLaunch/PreconnectNonSearchOmniboxSuggestions/Default/*PrefetchScheduler/Control_20250808/PrefetchServiceWorker/EnabledLaunch/*PreloadInlineDeferredImages/Holdback_DocumentPolicyExpectNoLinkedResources_20250731/PreloadTopChromeWebUILessNavigations/Control/*PreloadingConfig/Default_20250509/PreloadingNoSamePageFragmentAnchorTracking/Control_20250723/*PrewarmServiceWorkerRegistrationForDSE/Default/PrivacySandboxAdsApiUxEnhancements/EnabledLaunch/PrivacySandboxNoticeQueue/Control_20250815/PrivacySandboxPrivacyGuideAdTopics/EnabledLaunch/ProfileRemoteCommands/EnabledLaunch/ProgrammaticScrollAnimationOverride/Control_20250730/ProgressiveAccessibility/Default/ProtectedAudienceDealsSupport/Default/ProtectedAudienceKAnonymityKeyCacheStudy/EnabledLaunch/ProtectedAudienceTrustedSignalsKVv1CreativeScanning/Default/ProtectedAudiencesKAnonymityEnforcementStudy/DisabledFiller1_20250304/*QUIC/TcpBbrStableAug2025/*ReadAnythingReadAloudDesktop/Default/*ReduceCallingServiceWorkerRegisteredStorageKeysOnStartup/Default/*ReducePPMs/Control_20250819_50/RenderDocumentWithNavigationQueueing/Default_20250311/*RetroactivePreperiod/Default_Mac/*SearchPrefetchHighPriorityPrefetches/Default/*SeedFileTrial/Default/*ServiceWorkerBackgroundUpdateForRegisteredStorageKeys/Default/*ServiceWorkerBackgroundUpdateForServiceWorkerScopeCache/Default/*ServiceWorkerStaticRouterRaceNetworkRequestPerformanceImprovement/Default/*SharedDictionaryCache/Preperiod4/*SharedTabGroups/Default/ShoppingAlternateServer/Default/ShowSuggestionsOnAutofocus/EnabledLaunch/SidePanelResizing/EnabledLaunch/SignInPromoMaterialNextUI/Default/*SimdutfBase64Support/Enabled_SimdutfWithFurtherOptimization/*SkiaGraphite/Default_INTEL_20250507/SkipModerateMemoryPressureLevelMac/Control_20250819/*SlopBucket/Default/SoftNavigationDetectionAdvancedPaintAttribution/EnabledLaunch/SoftNavigationDetectionPrePaintBasedAttribution/Default/SqlScopedTransactionWebDatabase/Default/*SwiftShaderDeprecation/Default/TabCaptureInfobarLinks/Disabled_TabCaptureInfobarLinks/TabGroupShortcuts/EnabledLaunch/TabGroupSyncServiceDesktopMigrationRelaunch/EnabledLaunch/*TabSearchToolbarButtonNonEnUS/Default/TaskManagerDesktopRefresh/EnabledLaunch/TcpConnectionPoolSizeTrial/Default/*UKM/Enabled_20180314/*UMA-Population-Restrict/normal/UMA-Pseudo-Metrics-Effect-Injection-25-Percent/MediumEffect_05_20241105/*UMA-Uniformity-Trial-0.5-Percent-1/group_68/*UMA-Uniformity-Trial-0.5-Percent-2/group_141/*UMA-Uniformity-Trial-1-Percent/group_21/*UMA-Uniformity-Trial-10-Percent/group_05/*UMA-Uniformity-Trial-10-Percent-sanity/group_08/*UMA-Uniformity-Trial-100-Percent/group_01/*UMA-Uniformity-Trial-20-Percent/default/*UMA-Uniformity-Trial-20-Percent-Session/default/*UMA-Uniformity-Trial-5-Percent/group_09/*UMA-Uniformity-Trial-50-Percent/default/UkmSamplingRate/Downsampled_202309/*UnoDesktopBookmarksAndReadingList/Control_20250527/*UseAdHocSigningForWebAppShims/Default/*V8CodeFlushing/Default/V8IgnitionElideRedundantTdzChecksKillSwitch/Disabled_EmergencyKillSwitch/V8LargePagePool/Enabled_10/V8LateHeapLimitCheck/Control_50/VSyncAlignedPresent/EnabledLaunch/WasmTtsComponentUpdaterV3Enabled/Default/WebGPUEnableRangeAnalysisForRobustness/Default/*WebRTC-ForceDtls13/Default/WebRtcEncodedTransformDirectCallback/EnabledLaunch/WebUIInProcessResourceLoading/launched"}