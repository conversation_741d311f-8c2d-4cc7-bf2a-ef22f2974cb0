(()=>{"use strict";var e={676:function(e,t,n){var i,o=this&&this.__extends||(i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},i(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.init=void 0;var s=function(e){function t(t){return e.call(this,t,"aid-verify-widget","/verification")||this}return o(t,e),t}(n(381).WidgetLoader),a=null;t.init=function(e){a&&a.tearDown(),a=new s(r({},e)),e.callbacks.onSubmitHandle&&a.registerRPC("HandleSubmit","onSubmitHandle"),a.createIframeAndConnect()}},333:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var i=n(871);t.default=function(e,t,n,o,r,s,a){var c=this;if(null===e)throw"source cannot be null";this.connection=new i.WindowConnection(e,t,{namespace:o});var h={events:s,options:n.options,features:n.features,serviceKey:n.serviceKey,redirect_uri:window.location.origin,urlContext:new URL(n.serviceURL).pathname};n.devAppDomain&&(h.devAppDomain=n.devAppDomain),a&&a.length&&(h.procedures=a),this.connection.open(),this.connection.on("connected",(function(){n.callbacks.onReady&&n.callbacks.onReady({frameId:n.options.frameId}),c.connection.emit("config",h)})),this.connection.on("resize",(function(e){r.resize(e)}))}},864:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t,n,i,o,r){this.parameters={},this.expandedInitHeight=200,this.path=e,this.containerId=t,this.name=n,this.size=i,o&&(this.parameters=o);var s=document.createElement("iframe");s.src=this.getWidgetURL(),s.width="100%",s.height="expanded"!==i||r?r?"0":"100%":this.expandedInitHeight.toString(),s.id=n+"-iFrame",s.name=this.name,s.scrolling="no",s.frameBorder="0",s.setAttribute("role","none"),this.iframe=s;var a=document.getElementById(this.containerId);if(!a)throw new Error("cannot find element with id ".concat(t));this.container=a}return e.prototype.append=function(){return this.container.appendChild(this.iframe),this.iframe.contentWindow},e.prototype.destroy=function(){var e;null===(e=this.iframe.parentNode)||void 0===e||e.removeChild(this.iframe)},e.prototype.resize=function(e){this.iframe&&this.iframe.getBoundingClientRect().height<e.height&&"expanded"===this.size&&(this.iframe.height=Math.ceil(e.height).toString())},e.prototype.getWidgetURL=function(){var e=this,t=new URL(this.path);return Object.keys(this.parameters).forEach((function(n){"serviceKey"===n?t.searchParams.set("widgetKey",e.parameters[n]):t.searchParams.set(n,e.parameters[n])})),t.toString()},e}();t.default=n},381:function(e,t,n){var i=this&&this.__awaiter||function(e,t,n,i){return new(n||(n=Promise))((function(o,r){function s(e){try{c(i.next(e))}catch(e){r(e)}}function a(e){try{c(i.throw(e))}catch(e){r(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((i=i.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var n,i,o,r,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(r){return function(a){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,i&&(o=2&r[0]?i.return:r[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,r[1])).done)return o;switch(i=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,i=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(!((o=(o=s.trys).length>0&&o[o.length-1])||6!==r[0]&&2!==r[0])){s=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){s.label=r[1];break}if(6===r[0]&&s.label<o[1]){s.label=o[1],o=r;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(r);break}o[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(e){r=[6,e],i=0}finally{n=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,a])}}},r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.WidgetLoader=void 0;var s=n(642),a=r(n(333)),c=r(n(864)),h=function(){function e(e,t,n){this.callbackMapping={onComplete:"complete",onFail:"fail",onReady:"ready",onWidgetWillChangeFlow:"widgetWillChangeFlow",onWidgetWillRender:"widgetWillRender"},this.eventMapping={complete:"onComplete",fail:"onFail",ready:"onReady",widgetWillChangeFlow:"onWidgetWillChangeFlow",rpcRequest:"",widgetWillRender:"onWidgetWillRender"},this.procedures={},this.config=e,this.name=t,this.config.options.frameId?this.id=this.config.options.frameId:(this.id=(0,s.uuid)(),this.config.options.frameId=this.id),this.iframeParams=this.createIframeParameters(),this.config.features||(this.config.features={}),this.config.features.mode=this.config.features.mode?this.config.features.mode:"modal",this.config.features.size=this.config.features.size?this.config.features.size:"expanded",this.config.options.theme=this.config.options.theme?this.config.options.theme:{autoThemeAdjust:!1,defaultTheme:"light"},this.widgetIframe=new c.default(e.serviceURL+n,e.containerId,this.name,this.config.features.size,this.iframeParams,this.config.features.noPadding)}return e.prototype.registerRPC=function(e,t){this.procedures[e]=this.config.callbacks[t]},e.prototype.createIframeAndConnect=function(){var e=this;try{var t=this.widgetIframe.append(),n=Object.keys(this.config.callbacks).reduce((function(t,n){return"onCancel"!==n||t.includes("fail")?e.callbackMapping[n]&&!t.includes(e.callbackMapping[n])&&t.push(e.callbackMapping[n]):t.push("fail"),t}),[]),r=[];Object.keys(this.procedures).length>0&&(n.push("rpcRequest"),r=Object.keys(this.procedures));var s=new a.default(t,new URL(this.config.serviceURL).origin,this.config,this.name,this.widgetIframe,n,r);return n.forEach((function(t){var n=t;s.connection.on(n,(function(t){return i(e,void 0,void 0,(function(){var e,i,r,s,a;return o(this,(function(o){switch(o.label){case 0:return"rpcRequest"!==n?[3,2]:(e=t,this.procedures[e.procedure]?[4,this.procedures[e.procedure]({data:e.data,frameId:this.config.options.frameId})]:[2]);case 1:return i=o.sent(),this.sendResponse({uid:e.uid,data:i,procedure:e.procedure}),[3,3];case 2:r=t,s=t,"complete"!==n||r.token?"fail"===n&&"1007"===s.error.id?this.config.callbacks.onCancel&&this.config.callbacks.onCancel({data:s,frameId:this.config.options.frameId}):(a=this.eventMapping[n],this.config.callbacks[a]({data:t,frameId:this.config.options.frameId})):this.config.callbacks.onSkipVerification&&this.config.callbacks.onSkipVerification({data:r,frameId:this.config.options.frameId}),o.label=3;case 3:return[2]}}))}))}))})),this.connection=s.connection,s.connection}catch(e){throw new Error("cannot create and add iframe element to the DOM")}},e.prototype.createIframeParameters=function(){var e={serviceKey:this.config.serviceKey,redirect_uri:window.location.origin};if(this.config.devAppDomain&&(e.widgetDomain=this.config.devAppDomain),this.config.options.theme){var t=this.config.options.theme,n=t.autoThemeAdjust,i=t.defaultTheme;if("function"==typeof window.matchMedia&&n){var o=window.matchMedia("(prefers-color-scheme: dark)");o.matches&&(e.theme="dark"),o.onchange=function(){e.theme?delete e.theme:e.theme="dark"}}else"blue"!==i&&"dark"!==i||(e.theme="dark");this.config.options.theme.setColorScheme&&(this.config.options.theme.autoThemeAdjust&&(e.themeAdjust="1"),"blue"!==i&&"dark"!==i||(e.defaultTheme=i))}return!1===this.config.options.waitAnimation&&(e.w="0"),e},e.prototype.getIframeParameters=function(){return this.iframeParams},e.prototype.tearDown=function(){this.widgetIframe.destroy()},e.prototype.sendResponse=function(e){return this.connection.emit("rpcResponse",e),!0},e}();t.WidgetLoader=h},316:function(e,t,n){var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(676),t)},69:(e,t,n)=>{t.c=void 0;var i=n(316);t.c=function(){var e,t,n,o,r;null!==(e=window.AppleID)&&void 0!==e||(window.AppleID={}),null!==(t=(o=window.AppleID).service)&&void 0!==t||(o.service={}),null!==(n=(r=window.AppleID.service).verify)&&void 0!==n||(r.verify={}),window.AppleID.service.verify.init=i.init},(0,t.c)()},240:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EventEmitter=void 0,t.EventEmitter=class{constructor(){this.registry=new Map}emit(e,...t){var n;const i=null!==(n=this.registry.get(e))&&void 0!==n?n:[];for(const e of i)e(...t)}on(e,t){{let n=this.registry.get(e);n||this.registry.set(e,n=new Set),n.add(t)}return()=>{const n=this.registry.get(e);return!(!n||!n.has(t))&&(1===n.size?this.registry.delete(e):n.delete(t))}}getListeners(e){const t=this.registry.get(e);return t?[...t]:[]}}},101:function(e,t,n){var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(240),t)},642:function(e,t,n){var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.uid=void 0;let r=-1,s=0;t.uid=function(e){let t="";const n=Date.now();return r===n?s++:(s=0,r=n),t+=n+"-"+s,e&&(t=e+t),t},o(n(0),t)},0:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.uuid=void 0;const n=[4,6,8,10];t.uuid=()=>{const e=(16,crypto.getRandomValues(new Uint8Array(16)));e[6]=15&e[6]|64,e[8]=63&e[8]|128;let t="";for(let i=0;i<e.length;i++)n.includes(i)&&(t+="-"),t+=(e[i]+256).toString(16).substr(1);return t}},441:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WindowConnection=void 0;const i=n(101),o=n(642),r=n(72),s=new Set(["SYN","SYN/ACK","ACK"]),a=new Set([...s,"connected"]);class c extends i.EventEmitter{constructor(e,t,{source:n=window,timeout:i=5e3,namespace:o=""}={}){if(super(),this.target=e,this.targetOrigin=t,this.preConnectionEvents=[],"*"===t)throw new Error("targetOrigin can not be '*'");if(t.split("*").length>2)throw new Error("targetOrigin can only use 1 wildcard '*'");this.source=n,this.namespace=o,this.isConnected=!1,this.isListening=!1,this.timeout=i,this.logs=[]}handleMessageEvent(e){const t=this.targetOrigin.split("*"),n=t.length>1;if((n?e.origin.startsWith(t[0])&&e.origin.endsWith(t[1]):e.origin===this.targetOrigin)&&e.source===this.target&&(0,r.checkIsValidMessage)(this.namespace,e.data)){const t=(0,r.decode)(this.namespace,e.data),{winio:i,event:o}=t;s.has(o.name)||this.isConnected&&i.cid===this.connectionID?(n&&"SYN"===o.name&&(this.targetOrigin=e.origin),this.log({type:"on",data:t}),super.emit(o.name,o.payload)):this.preConnectionEvents.push(e)}}async startHandshake(){let e=0,t=0,n=Math.ceil(this.timeout/8);for(;e<3;)try{return 2===e&&n<this.timeout-t&&(n=this.timeout-t),void await this.attemptHandshake(n)}catch{t+=n,e+=1,n*=2}throw new Error("Handshake failed")}attemptHandshake(e){return this.handshakePromise=new Promise(((t,n)=>{this.resolveHandshake=t,setTimeout((()=>{this.isConnected||n()}),e)})),this.internalEmit("SYN",{connectionID:(0,o.uid)()}),this.handshakePromise}handshakeAcknowledged(e,t){var n;this.isConnected=!0,this.connectionID=e,null===(n=this.resolveHandshake)||void 0===n||n.call(this),super.emit("connected",t),this.preConnectionEvents.length>0&&(this.preConnectionEvents.forEach((e=>{this.handleMessageEvent(e)})),this.preConnectionEvents=[])}emit(e,t){if(a.has(e))throw new Error(`"${e}" is a reserve event.`);if(!this.isConnected)throw new Error("Connection has to be establish before emitting events.");this.internalEmit(e,t)}internalEmit(e,t){const n={winio:{cid:this.connectionID,eid:(0,o.uid)()},event:{name:e,payload:t}};this.log({type:"emit",data:n}),this.target.postMessage((0,r.encode)(this.namespace,n),this.targetOrigin)}open(e){if(this.isListening)throw new Error("Can't use both 'open' and 'connect' in the same connection");this.on("SYN",(t=>{this.internalEmit("SYN/ACK",{connectionID:t.connectionID,data:e})})),this.on("SYN/ACK",(t=>{this.internalEmit("ACK",{connectionID:t.connectionID,data:e}),this.handshakeAcknowledged(t.connectionID,t.data)})),this.on("ACK",(e=>{this.handshakeAcknowledged(e.connectionID,e.data)})),this.source.addEventListener("message",(e=>this.handleMessageEvent(e)),!1),this.isListening=!0}connect(e={}){if(this.isListening)throw new Error("Can't use both 'open' and 'connect' in the same connection");if(this.targetOrigin.includes("*"))throw new Error("Can't use 'connect' with a targetOrigin that uses a wildcard, please use 'open' instead to wait for a connection.");return this.open(e.data),this.startHandshake()}getLogs(){return this.logs}log(e){this.logs.push(e)}}t.WindowConnection=c},871:function(e,t,n){var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||i(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(441),t)},72:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.checkIsValidMessage=t.decode=t.encode=void 0;const n="winio:";t.encode=(e,t)=>`${n}${e}${JSON.stringify(t)}`,t.decode=(e,t)=>{const n=t.substring(6+e.length);return JSON.parse(n)},t.checkIsValidMessage=(e,t)=>"string"==typeof t&&t.startsWith(n)&&t.substr(6,e.length)===e}},t={};!function n(i){var o=t[i];if(void 0!==o)return o.exports;var r=t[i]={exports:{}};return e[i].call(r.exports,r,r.exports,n),r.exports}(69)})();